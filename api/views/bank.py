#!/usr/bin/env python
# -*- coding:utf-8 -*-
from django.views.generic import TemplateView
import calendar
from django.views.decorators.cache import cache_page
from django.db.models import F, ExpressionWrapper, FloatField
from django.db.models.functions import Greatest, Least, Cast
from django.shortcuts import render
from django.utils import timezone
import json
import time
from django.db.models import F, Min,Q
from django.db import transaction
from decimal import Decimal
from django.utils.decorators import method_decorator
from django.views.decorators.cache import never_cache
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from django.contrib.auth.models import User
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework import permissions, status, views
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from datetime import datetime, timedelta
from django.views.generic.list import ListView
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import permissions, status
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth.models import User
import uuid
import pytz
from django.db.models import Count, F
from rest_framework.views import APIView
from rest_framework.generics import ListAPIView, CreateAPIView, DestroyAPIView
from rest_framework.response import Response
from rest_framework import exceptions
from rest_framework import status
from rest_framework.filters import BaseFilterBackend
from api import models
from rest_framework.pagination import LimitOffsetPagination
from api.models import UserInfo,UserSymptom,Announcement, UserShareLog,Acupoint,Questionnaire,Question, Option,QuestionnaireResponse,UserCard, UserCard, Activity ,UserActivity,TCMQuestion
from api.serializers.serializers import TCMQuestionSerializer, AnnouncementSerializer
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.views import View
from django.contrib.auth.models import User
from django.conf import settings
import requests
import jwt
import random
from django.core.cache import cache
from django.views.decorators.cache import cache_page
from django_ratelimit.decorators import ratelimit
from api.views.tcmchat_refactored.base.decorators import api_timer
def check_static_settings(request):
    static_url = settings.STATIC_URL
    static_root = settings.STATIC_ROOT
    staticfiles_dirs = settings.STATICFILES_DIRS

    response_text = f"STATIC_URL: {static_url}\n"
    response_text += f"STATIC_ROOT: {static_root}\n"
    response_text += f"STATICFILES_DIRS: {staticfiles_dirs}\n"

    return HttpResponse(response_text, content_type="text/plain")

# 预渲染的静态HTML内容 - 避免每次请求都进行模板渲染
STATIC_HOME_HTML = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>靖江市天下式电子商务工作室 - 日月有数智能健康管理</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background: #f8fafc; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        .header { background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 1rem 0; }
        .nav { display: flex; justify-content: space-between; align-items: center; }
        .logo { display: flex; align-items: center; font-size: 1.5rem; font-weight: bold; color: #1f2937; }
        .sun-moon-icon { width: 40px; height: 40px; background: linear-gradient(to right, #f6d365 0%, #fda085 100%); border-radius: 50%; margin-right: 12px; position: relative; overflow: hidden; }
        .sun-moon-icon::after { content: ""; position: absolute; top: -15px; right: -15px; width: 40px; height: 40px; background: #f1f5f9; border-radius: 50%; }
        .main { padding: 3rem 0; }
        .hero { text-align: center; margin-bottom: 3rem; }
        .hero h1 { font-size: 2.5rem; font-weight: bold; margin-bottom: 1rem; color: #1f2937; }
        .hero p { font-size: 1.25rem; color: #6b7280; }
        .section { margin-bottom: 3rem; }
        .section h2 { font-size: 1.875rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937; }
        .section p { color: #6b7280; line-height: 1.6; }
        .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; }
        .feature { background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .feature h3 { font-size: 1.25rem; font-weight: 600; margin-bottom: 0.5rem; color: #1f2937; }
        .feature p { color: #6b7280; }
        .products { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 2rem; }
        .product { background: white; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); overflow: hidden; }
        .product-content { padding: 2rem; }
        .product h3 { font-size: 1.25rem; font-weight: 600; margin-bottom: 0.5rem; color: #1f2937; }
        .product p { color: #6b7280; }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <nav class="nav">
                <div class="logo">
                    <div class="sun-moon-icon"></div>
                    日月有数
                </div>
            </nav>
        </div>
    </header>

    <main class="main">
        <div class="container">
            <section class="hero">
                <h1>靖江市天下式电子商务工作室</h1>
                <p>专注于智能健康管理的创新解决方案</p>
            </section>

            <section class="section">
                <h2>公司介绍</h2>
                <p>天下式是一家专注于健康知识分享的创新公司。我们结合了最先进的人工智能技术，包括机器学习、深度学习和大语言模型，致力于为用户提供最优质的健康管理服务。</p>
            </section>

            <section class="section">
                <h2>软件特点</h2>
                <div class="features">
                    <div class="feature">
                        <h3>个性化健康评估</h3>
                        <p>根据用户的体质和健康数据定制养生方案</p>
                    </div>
                    <div class="feature">
                        <h3>智能健康跟踪</h3>
                        <p>日常健康跟踪和反馈，提供专业的健康知识库</p>
                    </div>
                    <div class="feature">
                        <h3>社交健康管理</h3>
                        <p>与志同道合的伙伴一起养生，分享健康经验</p>
                    </div>
                </div>
            </section>

            <section class="section">
                <h2>我们的产品</h2>
                <div class="products">
                    <div class="product">
                        <div class="product-content">
                            <h3>日月有数 - 标准版</h3>
                            <p>提供基础的健康评估和养生方案，适合大多数用户。</p>
                        </div>
                    </div>
                    <div class="product">
                        <div class="product-content">
                            <h3>日月有数 - 高级版</h3>
                            <p>在标准版的基础上，提供更加全面和详细的健康分析，以及一对一的健康顾问服务。</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>
</body>
</html>"""

from django.views.decorators.cache import never_cache
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View

# 全局缓存的响应内容，避免每次创建
_CACHED_HOME_CONTENT = None

def _get_cached_home_content():
    """获取缓存的首页内容"""
    global _CACHED_HOME_CONTENT
    if _CACHED_HOME_CONTENT is None:
        _CACHED_HOME_CONTENT = STATIC_HOME_HTML.encode('utf-8')
    return _CACHED_HOME_CONTENT

class HighPerformanceHomeView(View):
    """超高性能首页视图类 - 绕过大部分Django处理"""

    @method_decorator(csrf_exempt)
    def dispatch(self, request, *args, **kwargs):
        """重写dispatch方法，最小化处理"""
        return self.get(request, *args, **kwargs)

    def get(self, request, *args, **kwargs):
        """GET请求处理"""
        content = _get_cached_home_content()

        # 创建最小化的响应
        response = HttpResponse(
            content,
            content_type='text/html; charset=utf-8'
        )

        # 添加缓存头
        response['Cache-Control'] = 'public, max-age=21600'  # 6小时浏览器缓存
        response['ETag'] = '"homepage-v1"'
        response['Last-Modified'] = 'Wed, 30 Jul 2025 00:00:00 GMT'

        return response

# 创建视图实例
high_performance_home_view = HighPerformanceHomeView.as_view()

def home_page_with_timer(request):
    """极致性能首页视图 - 绕过所有缓存系统，直接返回静态内容"""
    # 检查ETag，支持304响应
    if request.META.get('HTTP_IF_NONE_MATCH') == '"homepage-v1"':
        response = HttpResponse(status=304)
        response['ETag'] = '"homepage-v1"'
        response['Cache-Control'] = 'public, max-age=21600'
        return response

    # 直接返回预编译的字节内容
    content = _get_cached_home_content()

    response = HttpResponse(
        content,
        content_type='text/html; charset=utf-8'
    )

    # 激进的缓存策略
    response['Cache-Control'] = 'public, max-age=21600, immutable'
    response['ETag'] = '"homepage-v1"'
    response['Last-Modified'] = 'Wed, 30 Jul 2025 00:00:00 GMT'
    response['Expires'] = 'Thu, 31 Jul 2025 00:00:00 GMT'

    return response

@cache_page(60 * 60 * 4)  # 缓存4小时
def terms(request):
    return render(request, 'web/terms.html')

@cache_page(60 * 60 * 4)  # 缓存4小时
def privacy(request):
    return render(request, 'web/privacy.html')

@cache_page(60 * 60 * 4)  # 缓存4小时
def contact(request):
    return render(request, 'web/contact.html')

class BankView(ListAPIView, CreateAPIView, DestroyAPIView):
    queryset = models.UserInfo.objects.all().order_by("-id")

    def get_serializer_class(self):
        if self.request.method == "POST":
            return BankCreateModelSerializer
        return BankListModelSerializer

    def delete(self, request, *args, **kwargs):
        user_object = self.get_object()
        from utils import ai
        ai.delete(user_object.uid, user_object.face_token)
        response = super().delete(request, *args, **kwargs)
        return response



class FaceView(APIView):
    def post(self, request, *args, **kwargs):
        avatar_object = request.data.get('avatar')
        if not avatar_object:
            return Response({"msg": "未提交图像", "status": False})

        # 假设每次拍照都对应一个测试用户（这里应该替换为实际的用户获取逻辑）
        user = models.User.objects.first()  # 假设获取第一个用户
        if user:
            token, created = Token.objects.get_or_create(user=user)
            return Response({
                "token": token.key,
                "status": True
            })
        else:
            return Response({"msg": "用户不存在", "status": False})

class VoiceView(APIView):
    def post(self, request, *args, **kwargs):
        voice_object = request.data.get('voice')
        from utils import ai
        result = ai.speed(voice_object)
        # {'corpus_no': '6847771638436561158', 'result': ['你是不是打过来？'], 'sn': '15921476781594371078', 'err_msg': 'success.', 'err_no': 0}
        return Response(result)


from rest_framework.filters import BaseFilterBackend


class PullDownFilter(BaseFilterBackend):
    def filter_queryset(self, request, queryset, view):
        max_id = request.query_params.get("max_id")
        if max_id:
            # [9, 8, 7 ]   [7,8,9]
            queryset = queryset.filter(id__gt=max_id)
            # queryset = queryset.filter(id__gt=max_id).reverse()
        return queryset


class ReachBottomFilter(BaseFilterBackend):
    def filter_queryset(self, request, queryset, view):
        min_id = request.query_params.get("min_id")
        if min_id:
            queryset = queryset.filter(id__lt=min_id)
        return queryset

class MineFilter(BaseFilterBackend):
    def filter_queryset(self, request, queryset, view):
        uid = request.query_params.get("user_id")
        if not uid:
            return queryset

        user_object = models.UserInfo.objects.filter(uid=uid).first()
        if not user_object:
            return queryset.none()

        # queryset = models.Activity.objects.filter(xxx=user_object)
        queryset = queryset.filter(ac__user=user_object)
        return queryset


from rest_framework.pagination import LimitOffsetPagination


class DemoLimitOffsetPagination(LimitOffsetPagination):
    default_limit = 2

    def get_offset(self, request):
        return 0

    def get_paginated_response(self, data):
        return Response(data)





from rest_framework import serializers





class ExchangeView(APIView):

    def get(self, request, *args, **kwargs):
        user_id = request.query_params.get('user_id')
        activity_id = request.query_params.get('activity_id')
        user_object = models.UserInfo.objects.filter(uid=user_id).first()
        record_object = models.JoinRecord.objects.filter(user=user_object, activity_id=activity_id).first()
        if not record_object:
            return Response({'status': False, "error": "数据不存在"})

        if record_object.exchange:
            return Response({'status': False, "error": "已申请，不能重复申请"})

        record_object.exchange = True
        record_object.save()

        user_object.score = user_object.score + record_object.activity.score
        user_object.save()

        return Response({'status': True, "msg": "申请成功", "score": user_object.score})
import logging
#logger = logging.getLogger(__name__)
logger = logging.getLogger(__name__)
##################会员系统###########

class ActivateMembershipView(View):
    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body.decode('utf-8'))
            openid = data.get('openid')

            if not openid:
                return JsonResponse({'status': 'error', 'message': '缺少 openid'}, status=400)

            user = UserInfo.objects.filter(openid=openid).first()
            if user:
                user.is_member = True
                user.member_duration += timedelta(days=31)  # 增加 31 天到会员时长
                user.score += 88  # 增加 88 积分
                user.member_EXP += 45  # 增加 55 点会员经验

                # 更新会员等级
                user.update_member_level()

                message = '会员续费成功，奖励88积分，恭喜发财！' if user.member_duration > timedelta(days=31) else '会员激活成功，奖励88积分，恭喜发财！'
                user.save()
                response_data = {
                    'status': 'success',
                    'message': message,
                    'openid': openid,
                    'nickname': user.nickname,
                    'avatar': user.avatar,
                    'score': user.score,
                    'level': user.Level,
                    'member_level': user.member_Level,  # 添加会员等级
                    'exp': user.EXP,
                    'member_exp': user.member_EXP,  # 添加会员经验
                    'is_member': user.is_member,
                    'member_duration': user.member_duration.total_seconds() / 86400  # 转换为天数
                }
                return JsonResponse(response_data)
            else:
                return JsonResponse({'status': 'error', 'message': '用户不存在'}, status=404)

        except Exception as e:
            logger.error(f"Error in ActivateMembershipView: {str(e)}")
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)





class WeChatLoginView(View):
    def post(self, request, *args, **kwargs):
        userInfo = request.POST.get('userInfo')
        code = request.POST.get('code')
        app_secret = settings.WX_APP_SECRET  # 应用的AppSecret，需要替换成您的真实AppSecret
        app_id = settings.WX_APP_ID
        url = f'https://api.weixin.qq.com/sns/jscode2session?appid={settings.app_id}&secret={app_secret}&js_code={code}&grant_type=authorization_code'
        print(url)
        response = requests.get(url)
        data = response.json()
        openid = data.get('openid')

        if openid:
            # 在这里处理用户信息的保存或更新
            user, created = UserInfo.objects.get_or_create(openid=openid, defaults={'nickname': userInfo['nickName'], 'avatar': userInfo['avatarUrl']})
            return JsonResponse({'status': 'success', 'uid': user.openid})
        else:
            return JsonResponse({'status': 'error', 'message': '获取 openid 失败'})


class checkmembershipView(View):
    def post(self, request, *args, **kwargs):
        # 从请求中获取由中间件设置的 user_id
        user_id = getattr(request, 'user_id', None)
        
        if not user_id:
            return JsonResponse({'error': '无法验证用户身份'}, status=401)
        
        try:
            # 使用 user_id 查询用户信息
            user = UserInfo.objects.get(id=user_id)
            
            # 检查会员状态
            user.check_membership_status()
            
            # 计算剩余天数
            remaining_days = user.get_remaining_days()
            
            response_data = {
                'ifmember': user.is_member,
                'memberlastingtime': user.member_duration,
                'exp': user.EXP,
                'level': user.Level,
                'score': user.score,
                'member_days': remaining_days,  # 使用新的计算方式
                'nickname': user.nickname,
                'avatar': user.avatar,
                'memberLevel': user.member_Level,
                'memberexp': user.member_EXP,
                'health_score': user.health_score,
                'consecutive_login_days': user.consecutive_login_days,
                'total_login_days': user.total_login_days,
                'check_in_count': user.check_in_count,
                # 添加新的会员相关信息
                'membership_end_date': user.membership_end_date.isoformat() if user.membership_end_date else None,
                'member_type': user.member_type,
                'login_method': user.login_method,
                'last_payment_time': user.last_payment_time.isoformat() if user.last_payment_time else None
            }
            return JsonResponse(response_data)
        except UserInfo.DoesNotExist:
            return JsonResponse({'error': '用户不存在'}, status=404)

    def get(self, request, *args, **kwargs):
        # 处理 GET 请求
        return HttpResponse("checkmembershipView is working properly.")
#只用于更新头像昵称
from django.http import HttpResponse
# class CheckOrRegisterUserView(View):
#     def get(self, request, *args, **kwargs):
#         # 简单返回一个确认信息
#         return HttpResponse("CheckOrRegisterUserView is working properly.")
    
#     def post(self, request, *args, **kwargs):
#         try:
#             # 从请求对象中获取由中间件设置的 user_id
#             user_id = getattr(request, 'user_id', None)
#             if not user_id:
#                 return JsonResponse({'status': 'error', 'message': '无法验证用户身份'}, status=401)

#             # 解析请求中的 JSON 数据
#             data = json.loads(request.body.decode('utf-8'))
#             user_info = data.get('userInfo', {})

#             # 检查用户是否存在，若不存在则注册
#             user, created = UserInfo.objects.get_or_create(id=user_id, defaults={
#                 'nickname': user_info.get('nickName', ''),
#                 'avatar': user_info.get('avatarUrl', ''),
#                 'has_uploaded_info': 'nickName' in user_info and 'avatarUrl' in user_info
#             })

#             # 如果用户已存在，更新用户信息
#             if not created:
#                 updated = False
#                 if user_info.get('nickName') and user.nickname != user_info.get('nickName'):
#                     user.nickname = user_info['nickName']
#                     updated = True
#                 if user_info.get('avatarUrl') and user.avatar != user_info.get('avatarUrl'):
#                     user.avatar = user_info['avatarUrl']
#                     updated = True
#                 if updated and not user.has_uploaded_info:
#                     user.has_uploaded_info = True
#                 user.save()

#             print(f"User {'created' if created else 'updated'}: {user.nickname}, UserID: {user.id}")
#             return JsonResponse({'status': 'success', 'message': '用户处理成功', 'is_member': user.is_member, '昵称': user.nickname})

#         except json.JSONDecodeError:
#             return JsonResponse({'status': 'error', 'message': '无效的 JSON 数据'}, status=400)
#         except Exception as e:
#             print(f"Error: {str(e)}")
#             return JsonResponse({'status': 'error', 'message': str(e)}, status=500)
from .content_review import HuaweiContentReview
import re
class CheckOrRegisterUserView(View):
    def get(self, request, *args, **kwargs):
        return HttpResponse("CheckOrRegisterUserView is working properly.")
    
    def post(self, request, *args, **kwargs):
        try:
            user_id = getattr(request, 'user_id', None)
            if not user_id:
                return JsonResponse({'status': 'error', 'message': '无法验证用户身份'}, status=401)
            
            # 解析请求中的 JSON 数据
            data = json.loads(request.body.decode('utf-8'))
            user_info = data.get('userInfo', {})
            
            # 获取昵称并进行内容审核
            nickname = user_info.get('nickName', '')
            if len(nickname) > 8:
                return JsonResponse({
                    'status': 'error',
                    'code': 'INVALID_NICKNAME',
                    'message': '昵称不能超过8个字'
                }, status=400)
            if not re.match(r'^[a-zA-Z0-9\u4e00-\u9fa5]+$', nickname):
                return JsonResponse({
                    'status': 'error',
                    'code': 'INVALID_NICKNAME',
                    'message': '昵称只能包含字母、数字和汉字'
                }, status=400)
            if nickname:
                content_reviewer = HuaweiContentReview(project_id=settings.HUAWEI_PROJECT_ID)
                review_result = content_reviewer.text_moderation(nickname)
                print(f"昵称审核结果: {json.dumps(review_result, ensure_ascii=False)}")
                
                # 检查审核结果 - 修复结果格式的处理
                if review_result.get('status') == 'success' and review_result.get('data', {}).get('suggestion') == 'block':
                    print(f"昵称 '{nickname}' 审核不通过")
                    return JsonResponse({
                        'status': 'error', 
                        'code': 'NICKNAME_BLOCKED',
                        'message': '昵称包含不当内容，请修改'
                    }, status=400)
            
            # 审核通过后的处理
            user, created = UserInfo.objects.get_or_create(id=user_id, defaults={
                'nickname': nickname,
                'avatar': user_info.get('avatarUrl', ''),
                'has_uploaded_info': 'nickName' in user_info and 'avatarUrl' in user_info
            })
            
            if not created:
                updated = False
                if nickname and user.nickname != nickname:
                    user.nickname = nickname
                    updated = True
                if user_info.get('avatarUrl') and user.avatar != user_info.get('avatarUrl'):
                    user.avatar = user_info['avatarUrl']
                    updated = True
                if updated and not user.has_uploaded_info:
                    user.has_uploaded_info = True
                user.save()
            
            print(f"User {'created' if created else 'updated'}: {user.nickname}, UserID: {user.id}")
            return JsonResponse({
                'status': 'success', 
                'message': '用户处理成功', 
                'is_member': user.is_member, 
                '昵称': user.nickname
            })
        
        except json.JSONDecodeError:
            return JsonResponse({'status': 'error', 'message': '无效的 JSON 数据'}, status=400)
        except Exception as e:
            print(f"Error: {str(e)}")
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

class AddSharePointsView(View):
    def post(self, request, *args, **kwargs):
        # 从请求中获取由中间件设置的 user_id
        user_id = getattr(request, 'user_id', None)
        
        if not user_id:
            return JsonResponse({'status': 'error', 'message': '无法验证用户身份'}, status=401)
        
        try:
            # 使用 user_id 查询用户信息
            user = UserInfo.objects.get(id=user_id)
            share_date = timezone.now().date()
            
            # 检查用户今天是否已经分享过
            if not UserShareLog.objects.filter(user=user, date=share_date).exists():
                # 增加积分和经验
                user.score += 10
                user.EXP += 5
                user.save()

                # 记录分享
                UserShareLog.objects.create(user=user, date=share_date)

                return JsonResponse({'status': 'success', 'message': '积分和经验已增加'})
            else:
                return JsonResponse({'status': 'info', 'message': '今天已分享过'})

        except UserInfo.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': '用户不存在'}, status=404)
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': f'服务器错误: {e}'}, status=500)












# 将变化的会员状态、经验值、等级，反馈到websocket
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer

def update_user_info(user_id, member_status=None, exp=None, level=None):
    
    message = {}
    if member_status is not None:
        message['member_status'] = member_status
    if exp is not None:
        message['exp'] = exp
    if level is not None:
        message['level'] = level

    channel_layer = get_channel_layer()
    async_to_sync(channel_layer.group_send)(
        f'user_{user_id}',
        {
            'type': 'send_user_update',
            'message': message
        }
    )


class AcupointListView(View):
    @method_decorator(ratelimit(key='ip', rate='30/m', block=True))
   
    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body)
            name = data.get('name', '')  # 从 POST 请求的数据中获取名字

            acupoints = Acupoint.objects.filter(name__icontains=name).values(
                'id', 'name', 'imageUrl', 'description_quxue', 
                'location', 'method_of_locating', 'massage_techniques', 
                'moxibustion_methods'
            )

            return JsonResponse(list(acupoints), safe=False)
        except json.JSONDecodeError:
            return JsonResponse({'error': '无效的 JSON 数据'}, status=400)

            



class AcupointDetailView(View):
    @method_decorator(ratelimit(key='ip', rate='30/m', block=True))

    @method_decorator(cache_page(60 * 15))  # 缓存15分钟
    def post(self, request, *args, **kwargs):
        data = json.loads(request.body.decode('utf-8'))
        name = data.get('name')

        if name:
            acupoint = Acupoint.objects.filter(name=name).values().first()
            if acupoint:
                return JsonResponse(acupoint, safe=False)
            return JsonResponse({'error': '穴位不存在'}, status=404)
        return JsonResponse({'error': '未提供穴位名称'}, status=400)



class QuestionnaireDetailView(View):
    @method_decorator(ratelimit(key='ip', rate='30/m', block=True))

    @method_decorator(cache_page(60 * 15))  # 缓存15分钟
    def post(self, request, *args, **kwargs):
        data = json.loads(request.body)
        questionnaire_id = data.get('id')
        if questionnaire_id:
            try:
                questionnaire = Questionnaire.objects.get(id=questionnaire_id)
                questions = Question.objects.filter(questionnaire=questionnaire)

                questions_data = []
                for question in questions:
                    options = question.options.values('text')#, 'probability', 'weight', 'type'
                    question_data = {
                        'text': question.text,
                        # 'options': list(options)
                        # 'options': [option['text'] for option in options]
                    }
                    questions_data.append(question_data)

                data = {
                    'questionnaire': questionnaire.name,
                    'questions': questions_data
                }
                return JsonResponse(data, safe=False)
            except Questionnaire.DoesNotExist:
                return JsonResponse({'error': '问卷不存在'}, status=404)
        return JsonResponse({'error': '未提供问卷ID'}, status=400)


from collections import defaultdict
class CalculateScoresView(View):
    def post(self, request, *args, **kwargs):
        try:
            # 从请求中解析 JSON 数据
            print('request',request)
            user_id = request.user_id
            print('user_id',user_id)
            data = json.loads(request.body)
            questionnaire_id = data.get('id')
            answers = data.get('answers', {})

            # 清除不需要的答案键，如 "undefined"
            answers = {k: v for k, v in answers.items() if k != "undefined"}

            # 设定系数数组，与前端保持一致
            multipliers = [0.0000001, 0.50002, 1.00003, 1.2000007]

            # 记录接收到的答案和问卷 ID
            # print(f"接收到的答案：{answers}")
            # print(f"问卷 ID：{questionnaire_id}")

            if questionnaire_id:
                try:
                    # 根据 ID 获取问卷和其对应的问题
                    questionnaire = Questionnaire.objects.get(id=questionnaire_id)
                    # print('questionnaire:',questionnaire)
                    questions = Question.objects.filter(questionnaire=questionnaire)
                    # print('questions:',questions)
                    # 使用 defaultdict 初始化一个存储结果的结构，以 text 为键
                    total_scores = defaultdict(lambda: {"probability": 0, "weight": 0, "type": None})
                    # total_scores = defaultdict(lambda: {"probability": 0, "weight": 0, "type": None})

                    # print('total_scores:',total_scores)
                    # 遍历每个问题
                    for question in questions:
                        # 提取问题的选项详情
                        options = list(question.options.values('text', 'probability', 'weight', 'type'))
                        # print('options',options)
                        # 获取用户对当前问题的回答索引
                        user_choice_index = answers.get(question.text, -1)
                        # print('user_choice_index:',user_choice_index)
                        # 验证用户选择的索引是否有效
                        if 0 <= user_choice_index < len(multipliers):
                            multiplier = multipliers[user_choice_index]
                            # print('multiplier',multiplier)
                            # 遍历问题的每个选项，计算乘积后加到结果中
                            for option in options:
                                text = option['text']
                                option_type = option['type']
                                # print(option_type)
                                # print('text', text)

                                # 如果该 text 还没有 type，则设置它
                                if total_scores[text]["type"] is None:
                                    total_scores[text]["type"] = option_type

                                # 累加权重和概率    
                                total_scores[text]['probability'] += option['probability'] * multiplier
                                total_scores[text]['weight'] += option['weight'] * multiplier

                    # 构建最终结果列表
                    final_results = [{"text": k, "probability": v["probability"], "weight": v["weight"]} for k, v in total_scores.items()]
                    # print('第一个final_results：',final_results)
                    final_results = [{"text": k, "probability": v["probability"], "weight": v["weight"], "type": v["type"]} for k, v in total_scores.items()]
                    
                    # print('第二个final_results：',final_results)
                    
                    user_id = request.user_id
                    # print('user_id',user_id)
                    # print('questionnaire_id:',questionnaire_id)
                    # print('answers：',answers)
                    # print('datetime.datetime.now()',datetime.now())
                    # 查询当前类别的问卷数量
                    category_count = QuestionnaireResponse.objects.filter(questionnaire_id=questionnaire_id).count()
                    
                    if category_count >= 100:
                        # 如果问卷数量已经达到100份，则删除该类别最老的问卷
                        oldest_response = QuestionnaireResponse.objects.filter(questionnaire_id=questionnaire_id).order_by('timestamp').first()
                        if oldest_response:
                            oldest_response.delete()
                    response = QuestionnaireResponse(
                        openid=str(user_id),
                        questionnaire_id=questionnaire_id,
                        answers=answers,
                        total_scores=final_results,
                        summary={"description": "问卷评分结果"},
                        timestamp=datetime.now()
                    )
                    response.save()
                    print(response)
                    # 以 JSON 格式返回最终结果
                    return JsonResponse('成功完成问卷评分', safe=False)

                    # return JsonResponse(final_results, safe=False)

                except Questionnaire.DoesNotExist:
                    # 若问卷不存在，则返回 404 错误
                    return JsonResponse({'error': '问卷不存在'}, status=404)

            # 若未提供问卷 ID，返回 400 错误
            return JsonResponse({'error': '未提供问卷ID'}, status=400)

        except Exception as e:
            # 发生其他错误，返回 500 错误
            print(f"错误: {str(e)}")
            return JsonResponse({'error': str(e)}, status=500)



class SaveQuestionnaireView(View):
    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body)
            openId = data.get('openId')
            questionnaireId = data.get('questionnaireId')
            answers = data.get('answers')
            totalScores = data.get('totalScores')
            summary = data.get('summary')
            timestamp = data.get('timestamp')

            # 保存数据到数据库
            response = QuestionnaireResponse.objects.create(
                openid=openId,
                questionnaire_id=questionnaireId,
                answers=answers,
                total_scores=totalScores,
                summary=summary,
                timestamp=timestamp
            )
            
            return JsonResponse({'status': 'success', 'message': '问卷数据已保存'})
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})

class CheckQuestionnaireFilledView(View):
    @method_decorator(ratelimit(key='ip', rate='10/m', block=True))
    def post(self, request, *args, **kwargs):
        data = json.loads(request.body)
        openid = data.get('openid')
        questionnaire_id = data.get('questionnaire_id')

        if not openid or not questionnaire_id:
            return JsonResponse({'error': '缺少必要参数'}, status=400)

        filled = QuestionnaireResponse.objects.filter(openid=openid, questionnaire_id=questionnaire_id).exists()
        return JsonResponse({'filled': filled})


#报错
class GetUserQuestionnairesView(View):
    # 这个类是用于读取用户问卷填写情况的
    @method_decorator(ratelimit(key='ip', rate='10/m', block=True))
    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body.decode('utf-8'))
            user_id = request.user_id
            # timestamp = data.get('timestamp')
            print(data)
            print(user_id)
            # 从数据库中检索特定用户的问卷响应
            responses = QuestionnaireResponse.objects.filter(openid=str(user_id))

            # 序列化数据
            response_data = [
                {   'user_id':user_id,
                    'questionnaire_id': response.questionnaire_id,
                    'answers': response.answers,
                    # 'total_scores': response.total_scores,
                    'summary': response.summary,
                    'timestamp': response.timestamp
                } for response in responses
            ]

            return JsonResponse(response_data, safe=False)
        except Exception as e:
            print("Error occurred:", e)  # 在控制台输出错误信息
            return JsonResponse({'status': 'error', 'message': str(e)})


class GetUserInfoView(View):
    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body.decode('utf-8'))
            openid = data.get('openid')
            if not openid:
                return JsonResponse({'status': 'error', 'message': '缺少 openid'}, status=400)

            user = UserInfo.objects.get(openid=openid)
            return JsonResponse({
                'status': 'success',
                'has_uploaded_info': user.has_uploaded_info
            })

        except UserInfo.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': '用户不存在'}, status=404)
        except json.JSONDecodeError:
            return JsonResponse({'status': 'error', 'message': '无效的 JSON 数据'}, status=400)
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)


            
import traceback
#写入打卡

import json
from django.views import View
from django.http import JsonResponse
from django.db import transaction
from django.db.models import F, Count, Q
from django.utils import timezone
import pytz
import calendar
from decimal import Decimal

from api.models import (
    UserInfo, Activity, UserActivity, UserGoal, BadHabitRecord, UserCard, AbstractGoal, AbstractGoalRecord
)
class UserCardView(View):
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body)
            user_id = request.user_id
            user = UserInfo.objects.select_for_update().get(id=user_id)

            date = data.get('date')
            activities_data = data.get('activities', [])  # 好习惯数据
            bad_habits_data = data.get('bad_habits', [])  # 坏习惯数据

            tz = pytz.timezone('Asia/Shanghai')
            if date:
                date_parsed = timezone.datetime.strptime(date, '%Y-%m-%d').date()
            else:
                date_parsed = timezone.now().astimezone(tz).date()

            # 检查是否已经在当天获得过积分
            existing_card = UserCard.objects.filter(user=user, date=date_parsed, score_added=True).exists()
            if existing_card:
                return JsonResponse({
                    'status': 'success',
                    'message': '今天已经打卡并获得积分，无法重复获得积分',
                    'check_in_count': user.check_in_count,
                    'health_score': user.health_score
                })

            user_card, created = UserCard.objects.get_or_create(
                user=user,
                date=date_parsed,
                defaults={'score_added': False}
            )

            completed_activities = 0
            
            # 处理好习惯活动数据
            for activity_data in activities_data:
                activity_id = activity_data.get('activity_id')
                activity_name = activity_data.get('activity_name')
                is_completed = activity_data.get('is_completed', True)  # 默认为已完成
                duration_minutes = activity_data.get('duration_minutes', 0)
                intensity = activity_data.get('intensity', 'medium')
                amount = activity_data.get('amount', '')
                notes = activity_data.get('notes', '')
                
                # 根据ID或名称获取活动
                if activity_id:
                    activity_obj = Activity.objects.get(id=activity_id)
                else:
                    # 先尝试查找现有活动（不区分大小写）
                    activity_name = activity_name.strip()
                    existing_activity = Activity.objects.filter(
                        name__iexact=activity_name,
                        is_bad_habit=False
                    ).first()
                    
                    if existing_activity:
                        activity_obj = existing_activity
                    else:
                        # 如果不存在，才创建新活动
                        activity_obj = Activity.objects.create(
                            name=activity_name,
                            is_custom=True,
                            created_by=user,
                            is_bad_habit=False
                        )
                
                # 创建或更新用户活动记录
                user_activity, _ = UserActivity.objects.update_or_create(
                    user=user,
                    activity=activity_obj,
                    date=user_card.date,
                    defaults={
                        'completed': is_completed,
                        'duration_minutes': duration_minutes,
                        'intensity': intensity,
                        'amount': amount,
                        'notes': notes
                    }
                )

                if is_completed:
                    completed_activities += 1
                    
                    # 更新用户目标的连续打卡记录
                    self._update_user_goal_streak(user, activity_obj, date_parsed)

            # 处理坏习惯数据 - 忠实记录每天的坏习惯，不再使用"避免"的概念
            for habit_data in bad_habits_data:
                habit_id = habit_data.get('habit_id')
                habit_name = habit_data.get('habit_name')
                avoided = habit_data.get('avoided', True)  # 是否避免了这个坏习惯
                notes = habit_data.get('notes', '')
                # 可以添加更多详细信息字段
                frequency = habit_data.get('frequency', '')  # 例如：坏习惯发生频率
                intensity = habit_data.get('intensity', '')  # 例如：坏习惯强度
                trigger = habit_data.get('trigger', '')      # 例如：触发坏习惯的原因
                
                # 根据ID或名称获取坏习惯
                if habit_id:
                    habit_obj = Activity.objects.get(id=habit_id, is_bad_habit=True)
                else:
                    # 先尝试查找现有坏习惯（不区分大小写）
                    habit_name = habit_name.strip()
                    existing_habit = Activity.objects.filter(
                        name__iexact=habit_name,
                        is_bad_habit=True
                    ).first()
                    
                    if existing_habit:
                        habit_obj = existing_habit
                    else:
                        # 如果不存在，才创建新坏习惯
                        habit_obj = Activity.objects.create(
                            name=habit_name,
                            is_bad_habit=True,
                            is_custom=True,
                            created_by=user
                        )
                
                # 创建或更新坏习惯记录
                BadHabitRecord.objects.update_or_create(
                    user=user,
                    habit=habit_obj,
                    date=date_parsed,
                    defaults={
                        'avoided': avoided,  # 记录是否避免了这个坏习惯
                        'notes': notes,
                        'intensity': habit_data.get('intensity', 'medium'),  # 添加程度字段处理
                        # 如果需要存储额外信息，可以将其序列化到notes字段中
                        # 或者修改BadHabitRecord模型添加新字段
                    }
                )
                
                # 如果成功避免坏习惯，也计入完成活动
                if avoided:
                    completed_activities += 1

            # 计算积分奖励
            if completed_activities > 0 and not user_card.score_added:
                health_score_increase = min(Decimal(completed_activities) * Decimal('0.5'), Decimal('3.0'))

                UserInfo.objects.filter(id=user.id).update(
                    check_in_count=F('check_in_count') + 1,
                    health_score=F('health_score') + health_score_increase
                )

                user_card.score_added = True
                user_card.save()

                user.refresh_from_db()

            return JsonResponse({
                'status': 'success',
                'message': '打卡数据已更新',
                'check_in_count': user.check_in_count,
                'health_score': user.health_score
            })

        except Exception as e:
            print(f"打卡提交错误: {str(e)}")
            traceback.print_exc()
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)
            
    def _update_user_goal_streak(self, user, activity, current_date):
        """更新用户目标的连续打卡记录"""
        try:
            # 查找该活动的未完成目标
            goal = UserGoal.objects.filter(
                user=user,
                activity=activity,
                is_completed=False,
                start_date__lte=current_date
            ).first()
            
            if not goal:
                return
                
            # 检查昨天是否有打卡
            yesterday = current_date - timezone.timedelta(days=1)
            yesterday_activity = UserActivity.objects.filter(
                user=user,
                activity=activity,
                date=yesterday,
                completed=True
            ).exists()
            
            # 如果昨天有打卡，增加连续天数
            if yesterday_activity:
                goal.current_streak += 1
            else:
                # 重置连续天数
                goal.current_streak = 1
                
            # 更新最长连续天数
            if goal.current_streak > goal.longest_streak:
                goal.longest_streak = goal.current_streak
                
            # 检查是否已完成目标
            if goal.current_streak >= goal.target_days:
                goal.is_completed = True
                
            goal.save()
            
        except Exception as e:
            print(f"更新目标连续记录出错: {str(e)}")
            traceback.print_exc()



#获取活动列表 API
class ActivityListView(View):
    def get(self, request, *args, **kwargs):
        try:
            user_id = request.user_id
            
            # 获取系统预设活动
            system_activities = Activity.objects.filter(is_custom=False, is_bad_habit=False)
            
            # 获取用户自定义活动
            custom_activities = Activity.objects.filter(created_by=user_id, is_bad_habit=False)
            
            # 合并结果，确保自定义活动排在前面，并去除重复活动
            activities_data = []
            activity_names_lower = set()  # 用于存储已添加的活动名称（小写）
            
            # 先添加用户自定义活动
            for activity in custom_activities:
                activity_name_lower = activity.name.strip().lower()
                if activity_name_lower not in activity_names_lower:
                    activity_names_lower.add(activity_name_lower)
                    activities_data.append({
                        'id': activity.id,
                        'name': activity.name,
                        'icon': activity.icon,
                        'description': activity.description,
                        'is_custom': True
                    })
            
            # 再添加系统预设活动（排除已添加的同名活动）
            for activity in system_activities:
                activity_name_lower = activity.name.strip().lower()
                if activity_name_lower not in activity_names_lower:
                    activity_names_lower.add(activity_name_lower)
                    activities_data.append({
                        'id': activity.id,
                        'name': activity.name,
                        'icon': activity.icon,
                        'description': activity.description,
                        'is_custom': False
                    })
            
            return JsonResponse(activities_data, safe=False)
            
        except Exception as e:
            print(f"获取活动列表错误: {str(e)}")
            traceback.print_exc()
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

# 获取坏习惯列表 API
class BadHabitListView(View):
    def get(self, request, *args, **kwargs):
        try:
            user_id = request.user_id
            
            # 获取系统预设坏习惯
            system_habits = Activity.objects.filter(is_custom=False, is_bad_habit=True)
            
            # 获取用户自定义坏习惯
            custom_habits = Activity.objects.filter(created_by=user_id, is_bad_habit=True)
            
            # 合并结果，确保自定义坏习惯排在前面
            habits_data = []
            
            # 先添加用户自定义坏习惯
            for habit in custom_habits:
                habits_data.append({
                    'id': habit.id,
                    'name': habit.name,
                    'icon': habit.icon,
                    'description': habit.description,
                    'is_custom': True
                })
                
            # 再添加系统预设坏习惯
            for habit in system_habits:
                habits_data.append({
                    'id': habit.id,
                    'name': habit.name,
                    'icon': habit.icon,
                    'description': habit.description,
                    'is_custom': False
                })
                
            return JsonResponse(habits_data, safe=False)
            
        except Exception as e:
            print(f"获取坏习惯列表错误: {str(e)}")
            traceback.print_exc()
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

#创建自定义活动/坏习惯 API
class CreateCustomActivityView(View):
    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body)
            user_id = request.user_id
            user = UserInfo.objects.get(id=user_id)
            
            name = data.get('name')
            description = data.get('description', '')
            icon = data.get('icon', '')
            is_bad_habit = data.get('is_bad_habit', False)
            
            print(f"接收到创建自定义活动请求: user_id={user_id}, name={name}, is_bad_habit={is_bad_habit}")
            
            if not name:
                print("错误: 活动名称为空")
                return JsonResponse({'status': 'error', 'message': '活动名称不能为空'}, status=400)
                
            # 检查是否已存在同名活动（不区分大小写）
            name = name.strip()
            existing = Activity.objects.filter(
                Q(created_by=user) | Q(created_by__isnull=True),
                name__iexact=name,
                is_bad_habit=is_bad_habit
            ).exists()
            
            if existing:
                print(f"错误: 已存在同名活动/习惯 '{name}'")
                return JsonResponse({'status': 'error', 'message': '已存在同名活动/习惯'}, status=400)
                
            # 创建新活动
            activity = Activity.objects.create(
                name=name,
                description=description,
                icon=icon,
                is_custom=True,
                created_by=user,
                is_bad_habit=is_bad_habit
            )
            
            print(f"成功创建活动: id={activity.id}, name={activity.name}, is_bad_habit={activity.is_bad_habit}")
            
            return JsonResponse({
                'status': 'success',
                'message': '创建成功',
                'activity': {
                    'id': activity.id,
                    'name': activity.name,
                    'description': activity.description,
                    'icon': activity.icon,
                    'is_bad_habit': activity.is_bad_habit,
                    'is_custom': True
                }
            })
            
        except Exception as e:
            import traceback
            print(f"创建自定义活动/习惯时发生错误:")
            print(f"错误类型: {type(e).__name__}")
            print(f"错误信息: {str(e)}")
            print("详细堆栈跟踪:")
            traceback.print_exc()
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)


#创建用户目标 API
class UserGoalView(View):
    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body)
            user_id = request.user_id
            user = UserInfo.objects.get(id=user_id)
            
            activity_id = data.get('activity_id')
            target_days = data.get('target_days', 30)
            start_date_str = data.get('start_date')
            daily_target_duration = data.get('daily_target_duration', 0)
            daily_target_amount = data.get('daily_target_amount', '')
            
            if not activity_id:
                return JsonResponse({'status': 'error', 'message': '请选择活动'}, status=400)
                
            try:
                activity = Activity.objects.get(id=activity_id)
            except Activity.DoesNotExist:
                return JsonResponse({'status': 'error', 'message': '活动不存在'}, status=400)
                
            # 解析开始日期
            if start_date_str:
                start_date = timezone.datetime.strptime(start_date_str, '%Y-%m-%d').date()
            else:
                start_date = timezone.now().date()
                
            # 检查是否已有相同活动的未完成目标
            existing_goal = UserGoal.objects.filter(
                user=user,
                activity=activity,
                is_completed=False
            ).first()
            
            if existing_goal:
                # 更新现有目标
                existing_goal.target_days = target_days
                existing_goal.start_date = start_date
                existing_goal.daily_target_duration = daily_target_duration
                existing_goal.daily_target_amount = daily_target_amount
                existing_goal.save()
                
                goal = existing_goal
                message = '目标已更新'
            else:
                # 创建新目标
                goal = UserGoal.objects.create(
                    user=user,
                    activity=activity,
                    target_days=target_days,
                    start_date=start_date,
                    daily_target_duration=daily_target_duration,
                    daily_target_amount=daily_target_amount
                )
                message = '目标已创建'
                
            return JsonResponse({
                'status': 'success',
                'message': message,
                'goal': {
                    'id': goal.id,
                    'activity_id': goal.activity.id,
                    'activity_name': goal.activity.name,
                    'target_days': goal.target_days,
                    'start_date': goal.start_date.isoformat(),
                    'end_date': goal.end_date.isoformat() if goal.end_date else None,
                    'daily_target_duration': goal.daily_target_duration,
                    'daily_target_amount': goal.daily_target_amount,
                    'current_streak': goal.current_streak,
                    'longest_streak': goal.longest_streak
                }
            })
            
        except Exception as e:
            print(f"创建/更新目标错误: {str(e)}")
            traceback.print_exc()
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)
            
    def get(self, request, *args, **kwargs):
        try:
            user_id = request.user_id
            
            # 获取用户的所有目标
            goals = UserGoal.objects.filter(user_id=user_id).select_related('activity')
            
            goals_data = []
            for goal in goals:
                goals_data.append({
                    'id': goal.id,
                    'activity_id': goal.activity.id,
                    'activity_name': goal.activity.name,
                    'target_days': goal.target_days,
                    'start_date': goal.start_date.isoformat(),
                    'end_date': goal.end_date.isoformat() if goal.end_date else None,
                    'daily_target_duration': goal.daily_target_duration,
                    'daily_target_amount': goal.daily_target_amount,
                    'is_completed': goal.is_completed,
                    'current_streak': goal.current_streak,
                    'longest_streak': goal.longest_streak,
                    'created_at': goal.created_at.isoformat() if goal.created_at else None
                })
                
            return JsonResponse(goals_data, safe=False)
            
        except Exception as e:
            print(f"获取目标列表错误: {str(e)}")
            traceback.print_exc()
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)



from datetime import datetime,date
#读取月度打卡
class UserCardMonthView(View):
    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body)
            print("Received data:", data)
            
            user_id = request.user_id
            
            tz = pytz.timezone('Asia/Shanghai')
            now = timezone.datetime.now(tz)
            
            print("Current time (Asia/Shanghai):", now.strftime("%Y-%m-%d %H:%M:%S %Z%z"))
            
            year = data.get('year', now.year)
            month = data.get('month', now.month)
            
            print("Processing for user:", user_id, "Year:", year, "Month:", month)
            
            start_date = timezone.datetime(year, month, 1).date()
            if month == 12:
                end_date = timezone.datetime(year + 1, 1, 1).date()
            else:
                end_date = timezone.datetime(year, month + 1, 1).date()
            
            # 获取用户卡片
            user_cards = UserCard.objects.filter(user=user_id, date__range=[start_date, end_date])
            
            # 获取用户活动
            user_activities = UserActivity.objects.filter(
                user_id=user_id,
                date__range=[start_date, end_date],
                completed=True
            ).select_related('activity')
            
            # 获取用户坏习惯
            bad_habits = BadHabitRecord.objects.filter(
                user_id=user_id,
                date__range=[start_date, end_date]
            ).select_related('habit')
            
            # 构建活动映射
            date_activities = {}
            for ua in user_activities:
                date_str = ua.date.isoformat()
                if date_str not in date_activities:
                    date_activities[date_str] = []
                date_activities[date_str].append({
                    'id': ua.activity.id,
                    'name': ua.activity.name,
                    'is_completed': ua.completed,
                    'is_bad_habit': False
                })
            
            for bh in bad_habits:
                date_str = bh.date.isoformat()
                if date_str not in date_activities:
                    date_activities[date_str] = []
                date_activities[date_str].append({
                    'id': bh.habit.id,
                    'name': bh.habit.name,
                    'is_completed': bh.avoided,
                    'is_bad_habit': True,
                    'avoided': bh.avoided
                })
            
            days_in_month = calendar.monthrange(year, month)[1]
            days_data = []
            
            for i in range(1, days_in_month + 1):
                date_obj = timezone.datetime(year, month, i).date()
                date_str = date_obj.isoformat()
                is_carded = user_cards.filter(date=date_obj).exists()
                
                day_data = {
                    'day': i,
                    'date': date_str,
                    'isCarded': is_carded,
                    'activities': date_activities.get(date_str, [])
                }
                days_data.append(day_data)
            
            print("Days data:", days_data)
            return JsonResponse(days_data, safe=False)
                
        except Exception as e:
            print("Exception occurred:", e)
            traceback.print_exc()
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

#查看特定日期活动
class DayActivitiesView(View):
    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body)
            user_id = request.user_id
            date_str = data.get('date')
            
            if date_str:
                date_obj = timezone.datetime.strptime(date_str, '%Y-%m-%d').date()
            else:
                date_obj = timezone.now().date()
                
            # 获取用户在该日期的活动记录
            user_activities = UserActivity.objects.filter(
                user_id=user_id,
                date=date_obj
            ).select_related('activity')
            
            # 获取用户在该日期的坏习惯记录
            bad_habits = BadHabitRecord.objects.filter(
                user_id=user_id,
                date=date_obj
            ).select_related('habit')
            
            # 构建返回数据
            activities_data = []
            for ua in user_activities:
                activities_data.append({
                    'id': ua.id,
                    'activity_id': ua.activity.id,
                    'activity_name': ua.activity.name,
                    'is_completed': ua.completed,
                    'duration_minutes': ua.duration_minutes,
                    'intensity': ua.intensity,
                    'amount': ua.amount,  # 运动量信息
                    'notes': ua.notes,
                    'is_bad_habit': False,
                    'is_custom': ua.activity.is_custom
                })
                
            for bh in bad_habits:
                activities_data.append({
                    'id': bh.id,
                    'activity_id': bh.habit.id,
                    'activity_name': bh.habit.name,
                    'is_completed': bh.avoided,
                    'notes': bh.notes,
                    'is_bad_habit': True,
                    'avoided': bh.avoided,
                    'intensity': bh.intensity,  # 添加程度字段
                    'is_custom': bh.habit.is_custom
                })
                
            # 检查是否已经获得积分
            user_card = UserCard.objects.filter(
                user_id=user_id,
                date=date_obj
            ).first()
            
            score_added = user_card.score_added if user_card else False
            
            return JsonResponse({
                'date': date_str,
                'activities': activities_data,
                'score_added': score_added
            })
            
        except Exception as e:
            print(f"获取日期活动错误: {str(e)}")
            traceback.print_exc()
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

class UserStatsView(View):
    def get(self, request, *args, **kwargs):
        try:
            print("开始处理用户统计请求")
            user_id = request.user_id
            print(f"用户ID: {user_id}")
            
            # 获取用户信息
            user = UserInfo.objects.get(id=user_id)
            print(f"成功获取用户信息: {user.nickname}")
            
            # 获取用户的所有目标
            goals = UserGoal.objects.filter(user_id=user_id).select_related('activity')
            print(f"获取到用户目标数量: {goals.count()}")
            
            # 获取用户的抽象目标
            abstract_goals = AbstractGoal.objects.filter(user_id=user_id)
            print(f"获取到用户抽象目标数量: {abstract_goals.count()}")
            
            # 获取用户的打卡记录
            today = timezone.now().date()
            last_30_days = today - timezone.timedelta(days=30)
            print(f"查询时间范围: {last_30_days} 至 {today}")
            
            user_cards = UserCard.objects.filter(
                user_id=user_id,
                date__gte=last_30_days
            ).order_by('date')
            print(f"获取到用户打卡记录数量: {user_cards.count()}")
            
            # 计算连续打卡天数
            current_streak = 0
            for i in range(30):
                check_date = today - timezone.timedelta(days=i)
                card_exists = user_cards.filter(date=check_date).exists()
                
                if card_exists:
                    current_streak += 1
                else:
                    break
            print(f"用户当前连续打卡天数: {current_streak}")
                    
            # 获取用户最常打卡的活动
            user_activities = UserActivity.objects.filter(
                user_id=user_id,
                date__gte=last_30_days,
                completed=True
            ).values('activity__name').annotate(
                count=Count('id')
            ).order_by('-count')[:5]
            print(f"获取到用户常用活动数量: {len(user_activities)}")
            
            top_activities = [
                {'name': ua['activity__name'], 'count': ua['count']}
                for ua in user_activities
            ]
            
            # 获取用户成功避免的坏习惯
            avoided_habits = BadHabitRecord.objects.filter(
                user_id=user_id,
                date__gte=last_30_days,
                avoided=True
            ).values('habit__name').annotate(
                count=Count('id')
            ).order_by('-count')[:5]
            print(f"获取到用户成功避免的坏习惯数量: {len(avoided_habits)}")
            
            top_avoided_habits = [
                {'name': ah['habit__name'], 'count': ah['count']}
                for ah in avoided_habits
            ]
            
            # 获取用户未能避免的坏习惯
            failed_habits = BadHabitRecord.objects.filter(
                user_id=user_id,
                date__gte=last_30_days,
                avoided=False
            ).values('habit__name').annotate(
                count=Count('id')
            ).order_by('-count')[:5]
            print(f"获取到用户未能避免的坏习惯数量: {len(failed_habits)}")
            
            top_failed_habits = [
                {'name': fh['habit__name'], 'count': fh['count']}
                for fh in failed_habits
            ]
            
            # 构建返回数据
            goals_data = []
            for goal in goals:
                # 计算目标进度
                progress = min(100, int((goal.current_streak / goal.target_days) * 100)) if goal.target_days > 0 else 0
                
                goals_data.append({
                    'id': goal.id,
                    'activity_name': goal.activity.name,
                    'target_days': goal.target_days,
                    'current_streak': goal.current_streak,
                    'longest_streak': goal.longest_streak,
                    'progress': progress,
                    'is_completed': goal.is_completed,
                    'is_abstract': False
                })
            
            # 添加抽象目标数据
            for goal in abstract_goals:
                # 计算目标进度
                progress = min(100, int((goal.current_streak / goal.target_days) * 100)) if goal.target_days > 0 else 0
                
                # 获取关联的活动和坏习惯名称
                activity_names = [activity.name for activity in goal.related_activities.all()]
                bad_habit_names = [habit.name for habit in goal.related_bad_habits.all()]
                
                goals_data.append({
                    'id': goal.id,
                    'goal_name': goal.goal_name,
                    'description': goal.description,
                    'target_days': goal.target_days,
                    'current_streak': goal.current_streak,
                    'longest_streak': goal.longest_streak,
                    'progress': progress,
                    'is_completed': goal.is_completed,
                    'is_abstract': True,
                    'related_activities': activity_names,
                    'related_bad_habits': bad_habit_names
                })
            
            print("成功构建用户统计数据，准备返回")
            return JsonResponse({
                'user': {
                    'nickname': user.nickname,
                    'check_in_count': user.check_in_count,
                    'health_score': float(user.health_score),
                    'current_streak': current_streak
                },
                'goals': goals_data,
                'top_activities': top_activities,
                'top_avoided_habits': top_avoided_habits,
                'top_failed_habits': top_failed_habits
            })
            
        except Exception as e:
            print(f"获取用户统计信息错误: {str(e)}")
            print(f"错误类型: {type(e).__name__}")
            print(f"请求参数: user_id={getattr(request, 'user_id', '未知')}")
            print(f"请求路径: {request.path}")
            print(f"请求方法: {request.method}")
            print(f"请求头: {dict(request.headers)}")
            
            import sys
            import traceback
            exc_type, exc_value, exc_traceback = sys.exc_info()
            print("完整异常信息:")
            traceback_details = {
                'filename': exc_traceback.tb_frame.f_code.co_filename,
                'lineno': exc_traceback.tb_lineno,
                'name': exc_traceback.tb_frame.f_code.co_name,
                'type': exc_type.__name__,
                'message': str(exc_value)
            }
            print(f"异常详情: {traceback_details}")
            print("异常堆栈跟踪:")
            traceback.print_exc()
            
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)


# 抽象目标打卡 API，这个接口没用到，感觉用不上啊 
class AbstractGoalRecordView(View):
    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body)
            user_id = request.user_id
            user = UserInfo.objects.get(id=user_id)
            
            goal_id = data.get('goal_id')
            is_completed = data.get('is_completed', True)
            notes = data.get('notes', '')
            date_str = data.get('date')
            
            if not goal_id:
                return JsonResponse({'status': 'error', 'message': '请选择目标'}, status=400)
                
            try:
                goal = AbstractGoal.objects.get(id=goal_id, user=user)
            except AbstractGoal.DoesNotExist:
                return JsonResponse({'status': 'error', 'message': '目标不存在'}, status=400)
                
            # 解析日期
            if date_str:
                date = timezone.datetime.strptime(date_str, '%Y-%m-%d').date()
            else:
                date = timezone.now().date()
                
            # 创建或更新打卡记录
            record, created = AbstractGoalRecord.objects.update_or_create(
                user=user,
                abstract_goal=goal,
                date=date,
                defaults={
                    'is_completed': is_completed,
                    'notes': notes
                }
            )
            
            # 更新目标的连续打卡记录
            self._update_abstract_goal_streak(user, goal, date)
            
            return JsonResponse({
                'status': 'success',
                'message': '打卡成功',
                'record': {
                    'id': record.id,
                    'goal_id': goal.id,
                    'goal_name': goal.goal_name,
                    'date': record.date.isoformat(),
                    'is_completed': record.is_completed,
                    'notes': record.notes
                }
            })
            
        except Exception as e:
            print(f"抽象目标打卡错误: {str(e)}")
            traceback.print_exc()
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)
            
    def _update_abstract_goal_streak(self, user, goal, current_date):
        """更新抽象目标的连续打卡记录"""
        try:
            # 检查昨天是否有打卡
            yesterday = current_date - timezone.timedelta(days=1)
            yesterday_record = AbstractGoalRecord.objects.filter(
                user=user,
                abstract_goal=goal,
                date=yesterday,
                is_completed=True
            ).exists()
            
            # 如果昨天有打卡，增加连续天数
            if yesterday_record:
                goal.current_streak += 1
            else:
                # 重置连续天数
                goal.current_streak = 1
                
            # 更新最长连续天数
            if goal.current_streak > goal.longest_streak:
                goal.longest_streak = goal.current_streak
                
            # 检查是否已完成目标
            if goal.current_streak >= goal.target_days:
                goal.is_completed = True
                
            goal.save()
            
        except Exception as e:
            print(f"更新抽象目标连续记录出错: {str(e)}")
            traceback.print_exc() 




# 删除用户打卡记录 API
class DeleteUserActivityView(View):
    def post(self, request, *args, **kwargs):
        try:
            user_id = request.user_id
            data = json.loads(request.body)
            
            # 获取必要参数
            activity_id = data.get('activity_id')
            date_str = data.get('date')
            
            if not activity_id or not date_str:
                return JsonResponse({'status': 'error', 'message': '缺少必要参数'}, status=400)
            
            try:
                # 解析日期
                date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                return JsonResponse({'status': 'error', 'message': '日期格式无效，请使用YYYY-MM-DD格式'}, status=400)
            
            # 获取用户
            try:
                user = UserInfo.objects.get(id=user_id)
            except UserInfo.DoesNotExist:
                return JsonResponse({'status': 'error', 'message': '用户不存在'}, status=404)
            
            # 获取活动
            try:
                activity = Activity.objects.get(id=activity_id)
            except Activity.DoesNotExist:
                return JsonResponse({'status': 'error', 'message': '活动不存在'}, status=404)
            
            # 查找并删除打卡记录
            try:
                user_activity = UserActivity.objects.get(
                    user=user,
                    activity=activity,
                    date=date_obj
                )
                
                # 保存记录信息用于返回
                activity_info = {
                    'id': user_activity.id,
                    'activity_name': activity.name,
                    'date': date_str,
                    'completed': user_activity.completed,
                    'duration_minutes': user_activity.duration_minutes,
                    'intensity': user_activity.intensity,
                    'amount': user_activity.amount,
                    'notes': user_activity.notes
                }
                
                # 删除记录
                user_activity.delete()
                
                # 如果有相关的目标，更新目标的连续打卡天数
                user_goals = UserGoal.objects.filter(user=user, activity=activity)
                for goal in user_goals:
                    # 重新计算连续打卡天数
                    self._recalculate_streak(goal)
                
                return JsonResponse({
                    'status': 'success', 
                    'message': '打卡记录已删除',
                    'deleted_activity': activity_info
                })
                
            except UserActivity.DoesNotExist:
                return JsonResponse({'status': 'error', 'message': '未找到指定的打卡记录'}, status=404)
            
        except Exception as e:
            print(f"删除打卡记录错误: {str(e)}")
            traceback.print_exc()
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)
    
    def _recalculate_streak(self, goal):
        """重新计算用户目标的连续打卡天数"""
        user = goal.user
        activity = goal.activity
        
        # 获取从开始日期到今天的所有打卡记录
        today = timezone.now().date()
        user_activities = UserActivity.objects.filter(
            user=user,
            activity=activity,
            date__gte=goal.start_date,
            date__lte=today,
            completed=True
        ).order_by('date')
        
        # 将日期转换为集合，方便查找
        completed_dates = set(ua.date for ua in user_activities)
        
        # 计算当前连续打卡天数
        current_streak = 0
        check_date = today
        
        while check_date in completed_dates:
            current_streak += 1
            check_date = check_date - timedelta(days=1)
        
        # 更新目标的连续打卡天数
        goal.current_streak = current_streak
        
        # 如果当前连续天数大于最长连续天数，更新最长连续天数
        if current_streak > goal.longest_streak:
            goal.longest_streak = current_streak
        
        goal.save()




# class UserCardView(View):
#     def post(self, request, *args, **kwargs):
#         try:
#             data = json.loads(request.body)
#             print("Received data:", data)
#             user_id = request.user_id
            
#             user = UserInfo.objects.get(id=user_id)
#             print(user)
            
#             date = data.get('date')
#             activities_data = data.get('activities', [])
#             print("Processing for user:", user_id, "Date:", date, "Activities:", activities_data)

#             # 使用东八区时间
#             tz = pytz.timezone('Asia/Shanghai')
            
#             # 解析日期，如果没有提供日期，则使用当前的东八区时间
#             if date:
#                 date_parsed = datetime.strptime(date, '%Y-%m-%d').date()
#             else:
#                 date_parsed = timezone.now().astimezone(tz).date()
            
#             print("Parsed date (Asia/Shanghai):", date_parsed)

#             # 获取或创建 UserCard 实例
#             user_card, created = UserCard.objects.get_or_create(
#                 user=user,
#                 date=date_parsed
#             )
#             print("User card:", "Created" if created else "Updated", user_card)

#             # 处理提交的活动
#             completed_activities = 0
#             for activity_data in activities_data:
#                 activity_name = activity_data.get('activity_name')
#                 is_completed = activity_data.get('is_completed', False)
#                 print("Processing activity:", activity_name, "Completed:", is_completed)

#                 # 更新或创建活动记录
#                 activity_obj, act_created = Activity.objects.get_or_create(name=activity_name)
#                 print("Activity object:", "Created" if act_created else "Found", activity_obj)

#                 user_activity, ua_created = UserActivity.objects.update_or_create(
#                     user=user,
#                     activity=activity_obj,
#                     date=user_card.date,
#                     defaults={'completed': is_completed}
#                 )
#                 print("User activity:", "Created" if ua_created else "Updated", user_activity)

#                 if is_completed:
#                     completed_activities += 1

#             if completed_activities > 0:
#                 # 计算健康分数增加：每个活动0.5分，最多1.5分
#                 health_score_increase = min(Decimal(completed_activities) * Decimal('0.5'), Decimal('1.5'))
                
#                 # 更新用户信息

#                 UserInfo.objects.filter(id=user.id).update(
#                     check_in_count=F('check_in_count') + 1,
#                     health_score=Greatest(0, Least(
#                         ExpressionWrapper(
#                             Cast(F('health_score'), FloatField()) + float(health_score_increase),
#                             output_field=FloatField()
#                         ),
#                         100
#                     ))
#                 )

#                 # 重新获取用户对象以获取更新后的值
#                 user.refresh_from_db()

#                 print(f"Updated user check-in count: {user.check_in_count}")
#                 print(f"Updated user health score: {user.health_score}")

#             return JsonResponse({
#                 'status': 'success', 
#                 'message': '打卡数据已更新',
#                 'check_in_count': user.check_in_count,
#                 'health_score': user.health_score
#             })

#         except Exception as e:
#             print("Exception occurred:", e)
#             traceback.print_exc()
#             return JsonResponse({'status': 'error', 'message': str(e)}, status=500)




class check_points(View):
    def post(self, request, *args, **kwargs):
        # 从请求中获取中间件设置的 user_id
        user_id = getattr(request, 'user_id', None)

        if not user_id:
            return JsonResponse({'status': 'error', 'message': '无法验证用户身份'}, status=401)

        try:
            data = json.loads(request.body)
            points_to_consume = data.get('points_to_consume', None)

            if points_to_consume is None:
                return JsonResponse({'status': 'error', 'message': '缺少积分消耗参数'}, status=400)

            user = UserInfo.objects.get(id=user_id)

            # 检查积分消耗值是否为负，即是否应该增加积分
            if points_to_consume < 0:
                # 增加积分
                user.score -= points_to_consume  # 由于 points_to_consume 为负值，这里实际上是增加积分
                user.save()  # 保存更改
                return JsonResponse({
                    'status': 'success',
                    'message': f'{-points_to_consume} points added.'
                })
            else:
                # 减少积分
                if user.score >= points_to_consume:
                    user.score -= points_to_consume  # 扣除积分
                    user.EXP += points_to_consume / 10  # 增加经验值，消耗积分的十分之一
                    user.save()  # 保存更改
                    return JsonResponse({
                        'status': 'success',
                        'message': f'积分足够。消费 {points_to_consume} 分，经验值增加 {points_to_consume / 10}。'
                    })
                else:
                    return JsonResponse({'status': 'error', 'message': '积分不足'}, status=400)

        except UserInfo.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': '用户不存在'}, status=404)
        except KeyError:
            return JsonResponse({'status': 'error', 'message': '请求数据无效'}, status=400)
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': f'服务器错误: {str(e)}'}, status=500)


# @method_decorator(csrf_exempt, name='dispatch')
class HealthExpRankingView(View):
    def post(self, request):
        top_users = UserInfo.objects.all().order_by('-EXP')[:100]
        users_list = [
            {'nickname': user.nickname, 'level': user.Level, 'exp': user.EXP}
            for user in top_users
        ]
        return JsonResponse(users_list, safe=False)

class MemberExpRankingView(View):
    def post(self, request):
        top_users = UserInfo.objects.all().order_by('-member_EXP')[:100]
        users_list = [
            {'nickname': user.nickname, 'member_level': user.member_Level, 'member_exp': user.member_EXP}
            for user in top_users
        ]
        return JsonResponse(users_list, safe=False)
# 每日题库系统
class DailyTCMQuestionView(APIView):
    def get(self, request, format=None):
        try:
            # 使用缓存避免频繁数据库查询
            cache_key = "daily_tcm_questions_all"
            questions = cache.get(cache_key)
            
            if questions is None:
                # 缓存未命中，查询数据库
                from django.db import connection
                try:
                    user_preferences = request.session.get('user_preferences', {})
                    print(user_preferences)

                    # 使用select_related优化查询
                    questions = list(TCMQuestion.objects.select_related('category').filter(**user_preferences))
                    
                    # 缓存结果5分钟
                    cache.set(cache_key, questions, timeout=300)
                finally:
                    # 确保连接被正确关闭
                    connection.close()
            
            # 随机选择题目
            daily_questions = random.sample(questions, min(10, len(questions))) if questions else []

            serializer = TCMQuestionSerializer(daily_questions, many=True)
            return Response(serializer.data)
        except Exception as e:
            print("An error occurred:", e)
            return Response({"error": f"An error occurred: {str(e)}"}, status=500)

    def post(self, request, format=None):
        try:
            # 使用缓存避免频繁数据库查询
            cache_key = "daily_tcm_questions_all"
            questions = cache.get(cache_key)
            
            if questions is None:
                # 缓存未命中，查询数据库
                from django.db import connection
                try:
                    user_preferences = request.session.get('user_preferences', {})
                    print(user_preferences)

                    # 使用select_related优化查询
                    questions = list(TCMQuestion.objects.select_related('category').filter(**user_preferences))
                    
                    # 缓存结果5分钟
                    cache.set(cache_key, questions, timeout=300)
                finally:
                    # 确保连接被正确关闭
                    connection.close()
            
            # 随机选择题目
            daily_questions = random.sample(questions, min(10, len(questions))) if questions else []

            serializer = TCMQuestionSerializer(daily_questions, many=True)
            return Response(serializer.data)
        except Exception as e:
            print("An error occurred:", e)
            return Response({"error": f"An error occurred: {str(e)}"}, status=500)
# @method_decorator(csrf_exempt, name='dispatch')


@method_decorator(csrf_exempt, name='dispatch')
class DailyQuizScoreView(View):
    @method_decorator(ratelimit(key='ip', rate='10/m', block=True))
    def post(self, request):
        try:
            # 从中间件获取的 user_id
            user_id = getattr(request, 'user_id', None)
            if not user_id:
                return JsonResponse({"error": "无法验证用户身份"}, status=401)

            # 确保正确解码请求体
            data = json.loads(request.body.decode('utf-8'))
            score = data.get('score')

            if score is None:
                return JsonResponse({"error": "缺少分数信息"}, status=400)

            # 根据 user_id 获取用户信息
            print(f"检查uid: {user_id}")  # 添加调试信息
            user = UserInfo.objects.get(pk=user_id)
            user.daily_quiz_score = score
            user.save()

            return JsonResponse({"message": "分数更新成功"}, status=200)
        except UserInfo.DoesNotExist:
            print(f"User with uid {user_id} not found")  # 添加调试信息
            return JsonResponse({"error": "用户未找到"}, status=404)
        except json.JSONDecodeError:
            return JsonResponse({"error": "无效的 JSON 数据"}, status=400)
        except Exception as e:
            print(f"An unexpected error occurred: {str(e)}")  # 添加调试信息
            return JsonResponse({"error": str(e)}, status=500)

@method_decorator(csrf_exempt, name='dispatch')         
class DailyQuizRankingView(View):
    @method_decorator(ratelimit(key='ip', rate='10/m', block=True))
    def get(self, request):
        # 获取按照每日答题得分排序的用户列表，这里获取前100名
        top_users = UserInfo.objects.all().order_by('-daily_quiz_score')[:100]

        # 构造返回的用户信息列表
        users_list = [
            {
                'nickname': user.nickname,
                'daily_quiz_score': user.daily_quiz_score,
                'rank': index + 1  # 计算排名
            }
            for index, user in enumerate(top_users)
        ]

        return JsonResponse(users_list, safe=False)

class UserCreateView(APIView):
    permission_classes = [permissions.AllowAny]

    def get(self, request):
        # 这是一个示例GET请求处理方法，返回一个简单的消息
        return Response({"message": "This is a GET request. Use POST to create a user."})

    def post(self, request):
        username = str(uuid.uuid4())
        user = User.objects.create_user(username=username)
        refresh = RefreshToken.for_user(user)
        
        return Response({
            'username': user.username,
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        }, status=status.HTTP_201_CREATED)

class UIDTokenObtainPairSerializer(TokenObtainPairSerializer):
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)

        # 添加自定义声明，这里我们用不到email，所以我去掉了
        token['username'] = user.username

        return token

class UIDTokenObtainPairView(TokenObtainPairView):
    permission_classes = [permissions.AllowAny]
    serializer_class = UIDTokenObtainPairSerializer

    def post(self, request, *args, **kwargs):
        uid = request.data.get('uid')
        if not uid:
            return Response({"error": "UID is required"}, status=status.HTTP_400_BAD_REQUEST)

        # 假设UID存在于UserInfo模型的openid字段中
        user_info = UserInfo.objects.filter(openid=uid).first()
        if not user_info:
            # UID不存在，创建新用户
            user_info = UserInfo.objects.create(openid=uid)
            user = User.objects.create_user(username=uid)
        else:
            # UID存在，获取对应的User实例
            user = User.objects.get(username=uid)

        refresh = RefreshToken.for_user(user)
        return Response({
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        }, status=status.HTTP_200_CREATED)

class CustomTokenRefreshView(TokenRefreshView):
    def post(self, request, *args, **kwargs):
        # 在这里，你可以添加任何自定义逻辑，比如验证请求中的额外数据
        
        # 调用父类的post方法来实现Token刷新
        response = super().post(request, *args, **kwargs)
        
        # 你也可以在这里修改response，或者基于某些条件返回不同的响应
        # 例如，你可以检查response.data中是否存在错误，并据此做出调整
        
        return response

class HomeView(TemplateView):
    template_name = 'web/home.html'

def simulate_wechat_api(code):
    # 此函数应调用微信的API来获取openid和session_key
    # 此处简化为假数据
    return ("fake-openid", "fake-session_key")




#现在用的微信注册登录，返回token
class LoginByWeixinView_new1(View):
    def post(self, request, *args, **kwargs):
        try:
            print('接收到POST请求')
            data = json.loads(request.body.decode('utf-8'))
            code = data.get('code')
            print('接收到的code', code)
            if not code:
                return JsonResponse({'error': '缺少 code 参数'}, status=400)

            app_id = settings.WX_APP_ID
            app_secret = settings.WX_APP_SECRET

            wx_login_url = f"https://api.weixin.qq.com/sns/oauth2/access_token?appid={app_id}&secret={app_secret}&code={code}&grant_type=authorization_code"
            print(wx_login_url)
            response = requests.get(wx_login_url)
            wx_response_data = response.json()
            print('解析的请求数据', wx_response_data)

            if 'openid' in wx_response_data:
                wx_openid_new = wx_response_data['openid']
                wx_unionid_new = wx_response_data.get('unionid')

                # 检查微信ID是否在封禁列表中
                from api.models.user_models import BannedAccount
                is_banned, banned_info = BannedAccount.check_banned(
                    phone=None, 
                    openid=wx_openid_new,
                    unionid=wx_unionid_new
                )
                

                user = None
                if wx_unionid_new:
                    try:
                        user = UserInfo.objects.get(wx_unionid_new=wx_unionid_new)
                        print('用户已存在')
                        
                        # 检查用户是否被封禁
                        if user.is_banned:
                            ban_info = {
                                'error': '您的账号已被封禁',
                                'reason': user.ban_reason,
                                'ban_time': user.ban_time.isoformat() if user.ban_time else None,
                            }
                            
                            # 如果有封禁截止时间，检查是否已过期
                            if user.ban_until:
                                if user.ban_until > timezone.now():
                                    ban_info['ban_until'] = user.ban_until.isoformat()
                                    return JsonResponse(ban_info, status=403)
                                else:
                                    # 封禁已过期，解除封禁
                                    user.is_banned = False
                                    # 继续正常流程
                            else:
                                # 没有截止时间，返回封禁消息
                                return JsonResponse(ban_info, status=403)
                                
                    except UserInfo.DoesNotExist:
                        user = UserInfo(wx_openid_new=wx_openid_new, wx_unionid_new=wx_unionid_new)
                        print('创建新用户')

                user.wx_session_key_new = wx_response_data.get('session_key')
                user.wx_unionid_new = wx_response_data.get('unionid')
                user.wx_phone_new = wx_response_data.get('phone', '')
                user.wx_access_token = wx_response_data.get('access_token')
                user.wx_refresh_token = wx_response_data.get('refresh_token')
                user.wx_access_token_expires = timezone.now() + timedelta(seconds=wx_response_data.get('expires_in', 7200))
                user.login_method = 'WECHAT'
                user.save()

                # 生成访问token和刷新token
                access_token = self.generate_access_token(user)
                refresh_token = self.generate_refresh_token(user)

                response_data = {
                    'access_token': access_token,
                    'refresh_token': refresh_token
                }
                print('返回用户信息或者创建新用户的信息')
                print(JsonResponse(response_data))
                return JsonResponse(response_data)
            else:
                return JsonResponse({'error': '微信登录失败', 'details': wx_response_data.get('errmsg', '无错误信息')})
        except Exception as e:
            print("发生错误:")
            print(traceback.format_exc())  # 这将打印完整的错误堆栈跟踪
            return JsonResponse({'error': '服务器错误', 'details': str(e)}, status=500)

    def generate_access_token(self, user):
        payload = {
            'user_id': user.id,
            'wx_openid': user.wx_openid_new,
            'wx_unionid': user.wx_unionid_new,
            'exp': datetime.utcnow() + timedelta(hours=6)  # 12小时过期
            # 'exp': datetime.utcnow() + timedelta(seconds=15),  # 15秒后过期

        }
        return jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')

    def generate_refresh_token(self, user):
        payload = {
            'user_id': user.id,
            'exp': datetime.utcnow() + timedelta(days=90)  # 45天过期
        }
        return jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')
#现在用的手机号注册登录，返回token
class RegisterOrLoginByPhoneView(View):
    def post(self, request, *args, **kwargs):
        data = json.loads(request.body.decode('utf-8'))
        print(data)
        phone_number = data.get('phoneNumber')
        print(phone_number)
        if not phone_number:
            return JsonResponse({'error': '缺少 phoneNumber 参数'}, status=400)

        # 检查手机号是否在封禁列表中
        from api.models.user_models import BannedAccount
        is_banned, banned_info = BannedAccount.check_banned(
            phone=phone_number,
            openid=None,
            unionid=None
        )
        
        if is_banned:
            ban_info = {
                'error': '您的账号已被封禁',
                'reason': banned_info.get_reason_display(),
                'detail': banned_info.detail,
                'expire_at': banned_info.expire_at.isoformat() if banned_info.expire_at else None
            }
            return JsonResponse(ban_info, status=403)

        user, created = UserInfo.objects.get_or_create(wx_phone_new=phone_number)
        if created:
            print('创建新用户')
        else:
            print('用户已存在')

        user.last_login = timezone.now()
        user.login_method = 'PHONE'
        user.save()
        print('user.id:', user.id)

        # 生成访问token和刷新token
        access_token = self.generate_access_token(user)
        refresh_token = self.generate_refresh_token(user)

        response_data = {
            'access_token': access_token,
            'refresh_token': refresh_token
        }
        return JsonResponse(response_data)

    def generate_access_token(self, user):
        payload = {
            'user_id': user.id,
            'phone_number': user.wx_phone_new,
            'exp': datetime.utcnow() + timedelta(hours=1)  # 1小时过期
            # 'exp': datetime.utcnow() + timedelta(seconds=15),  # 15秒后过期

        }
        return jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')

    def generate_refresh_token(self, user):
        payload = {
            'user_id': user.id,
            'exp': datetime.utcnow() + timedelta(days=90)  # 14天过期
        }
        return jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')

class RefreshTokenView(View):
    def post(self, request, *args, **kwargs):
        # 打印原始请求数据
        print("Received raw request body:", request.body)
        print("Received headers:", request.headers)

        try:
            # 尝试从请求头中获取刷新令牌
            refresh_token = request.headers.get('Refresh-Token')
            
            if not refresh_token:
                # 如果请求头中没有，尝试从请求体解析 JSON
                data = json.loads(request.body.decode('utf-8'))
                refresh_token = data.get('refresh_token')

            if not refresh_token:
                print("错误: 缺少 refresh_token")
                return JsonResponse({'error': '缺少 refresh_token'}, status=400)

            print("Extracted refresh token:", refresh_token)

            try:
                # 验证刷新token
                payload = jwt.decode(refresh_token, settings.SECRET_KEY, algorithms=['HS256'])
                user_id = payload.get('user_id')
                user = UserInfo.objects.get(id=user_id)

                # 生成新的访问token
                new_access_token = self.generate_access_token(user)

                return JsonResponse({
                    'access_token': new_access_token
                })
            except jwt.ExpiredSignatureError as e:
                print(f"错误: 刷新token已过期 - {str(e)}")
                return JsonResponse({'error': '刷新token已过期'}, status=466)
            except jwt.InvalidTokenError as e:
                print(f"错误: 无效的刷新token - {str(e)}")
                return JsonResponse({'error': '无效的刷新token'}, status=400)
            except UserInfo.DoesNotExist as e:
                print(f"错误: 用户不存在 - {str(e)}")
                return JsonResponse({'error': '用户不存在'}, status=400)
            except Exception as e:
                print(f"错误: 刷新token时发生未知错误 - {str(e)}")
                return JsonResponse({'error': '刷新token时发生错误'}, status=500)

        except json.JSONDecodeError as e:
            print(f"错误: JSON 解码错误 - {str(e)}")
            return JsonResponse({'error': '无效的 JSON 数据'}, status=400)
        except Exception as e:
            print(f"错误: 处理请求时发生未知错误 - {str(e)}")
            return JsonResponse({'error': '处理请求时发生错误'}, status=500)

    def generate_access_token(self, user):
        # 实现生成新访问token的逻辑
        payload = {
            'user_id': user.id,
            'exp': datetime.utcnow() + timedelta(hours=6)  # 1小时过期
        }
        return jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')


from django.contrib.auth import logout as django_logout
from django.http import JsonResponse

class LogoutView(View):
    def post(self, request):
        try:
            # 从中间件获取的 user_id
            user_id = getattr(request, 'user_id', None)
            
            if not user_id:
                return JsonResponse({'error': '用户未认证'}, status=401)
            
            # 获取用户并更新最后登出时间
            try:
                user = UserInfo.objects.get(id=user_id)
                user.last_logout = timezone.now()
                
                # 清除或更新与登录相关的字段
                user.wx_session_key_new = None
                user.wx_access_token = None
                user.wx_refresh_token = None
                user.wx_access_token_expires = None
                
                user.save()
                
                # 在实际生产环境中，您可能还想执行以下操作：
                # 1. 将当前token加入黑名单（需要额外的数据结构）
                # blacklist_token(request.auth_token)
                
                # 2. 记录用户注销日志
                # log_user_logout(user_id, user.wx_openid_new, user.wx_unionid_new)
                
                return JsonResponse({'message': '注销成功'})
            except UserInfo.DoesNotExist:
                return JsonResponse({'error': '用户不存在'}, status=404)
            
        except Exception as e:
            print("注销过程中发生错误:")
            print(traceback.format_exc())  # 这将打印完整的错误堆栈跟踪
            return JsonResponse({'error': '服务器错误', 'details': str(e)}, status=500)


        
class HelloView11(View):
    @method_decorator(never_cache)
    def get(self, request, *args, **kwargs):
        return HttpResponse("abc1111111")

from django.views.decorators.http import require_http_methods
from django.db.models import Max, OuterRef, Subquery
@require_http_methods(["GET", "POST"])  # 允许 GET 和 POST 请求
def simple_view(request):
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return HttpResponse(f"Current time: {current_time}")




from concurrent.futures import ThreadPoolExecutor


# @method_decorator(cache_page(60 * 15), name='dispatch')
# class AnalyzeQuestionnaireView(View):
#     MAX_WEIGHTS = {
#         # 11种基础证型的最大权重
#         '气虚': 160.8,
#         '气滞': 152.4,
#         '阳虚': 102,
#         '痰症': 128.4,
#         '实热': 98.4,
#         '阴虚': 57.6,
#         '实寒': 68.4,
#         '湿症': 94.8,
#         '血虚': 63.6,
#         '血瘀': 38.4,
#         '上热下寒': 20,#3.6
#         '热证': 1111,
#         # 五个脏腑的最大权重
#         '肺': 62.4,
#         '脾': 295.2,
#         '肝': 214.8,
#         '肾': 200.4,
#         '心': 81.6
#     }
#     def post(self, request, *args, **kwargs):
#         start_time = time.time()  # 记录开始时间
#         try:
#             user_id = request.user_id
#             # 获取最新问卷记录
#             latest_timestamps_start = time.time()
#             latest_timestamps = QuestionnaireResponse.objects.filter(
#             openid=str(user_id),
#             questionnaire_id__gte=10,  # 筛选 questionnaire_id 大于等于 10
#             questionnaire_id__lte=18   # 筛选 questionnaire_id 小于等于 18
#         ).values('questionnaire_id').annotate(
#             latest_time=Max('timestamp')
#         ).values('questionnaire_id', 'latest_time')
#             latest_timestamps_end = time.time()
#             print(f"获取最新问卷记录时间: {latest_timestamps_end - latest_timestamps_start} 秒")

#             # 获取最新响应
#             latest_responses_start = time.time()
#             latest_responses = QuestionnaireResponse.objects.filter(
#                 openid=str(user_id),
#                 questionnaire_id__gte=10,  # 筛选 questionnaire_id 大于等于 10
#                 questionnaire_id__lte=18,  # 筛选 questionnaire_id 小于等于 18
#                 timestamp=Subquery(
#                     latest_timestamps.filter(questionnaire_id=OuterRef('questionnaire_id')).values('latest_time')
#                 )
#             )
#             latest_responses_end = time.time()
#             print(f"获取最新响应时间: {latest_responses_end - latest_responses_start} 秒")
#             print(latest_timestamps.explain())
#             total_scores = defaultdict(lambda: {"totalWeight": 0, "totalProbability": 0, "type": None})

#             max_weights = {}  # 记录每个项目的最大权重
#             processing_start = time.time()


#             for response in latest_responses:
#                 scores = response.total_scores
#                 # print(f"处理问卷 ID: {response.questionnaire_id}, 答案: {response.answers}")

#                 for score in scores:
#                     text = score['text']
#                     weight = score['weight']
#                     probability = score['probability']
#                     option_type = score['type']

#                     # 使用局部变量减少字典访问次数
#                     if text not in total_scores:
#                         total_scores[text] = {"totalWeight": 0, "totalProbability": 0, "type": None}
#                     total_score = total_scores[text]

#                     total_score['totalWeight'] += weight
#                     total_score['totalProbability'] += probability

#                     if total_score['type'] is None:
#                         total_score['type'] = option_type

#                     if text not in max_weights:
#                         max_weights[text] = weight
#                     else:
#                         max_weights[text] = max(max_weights[text], weight)
#             processing_end = time.time()
#             print(f"处理响应时间: {processing_end - processing_start} 秒")
#             final_results = [{"text": k, "probability": v['totalProbability'], "weight": v['totalWeight'], "type": v['type']} for k, v in total_scores.items()]
#             # print('完整结果：', final_results)

#             basic_indicators = []
#             independent_indicators = []
#             final_processing_start = time.time()
#             for item in final_results:
#                 max_weight = self.MAX_WEIGHTS.get(item['text'], 1)
#                 adjusted_weight = round((item['weight'] / max_weight) * 100, 2)
#                 severity = self.get_severity_description(adjusted_weight)

#                 # print('循环中测试证型返回',zhengxing_detail)
#                 indicator_data = {
#                     "name": item['text'],
#                     "type":item['type'],
#                     # "probability": item['probability'],
#                     # "weight": item['weight'],
#                     "adjustedWeight": adjusted_weight,
#                     "severity": severity
#                 }

#                 if item['type'] == "基础":
#                     basic_indicators.append(indicator_data)
#                 else:
#                     independent_indicators.append(indicator_data)

#             basic_indicators.sort(key=lambda x: x['adjustedWeight'], reverse=True)
#             independent_indicators.sort(key=lambda x: x['adjustedWeight'], reverse=True)
#             final_processing_end = time.time()
#             print(f"最终处理时间: {final_processing_end - final_processing_start} 秒")

#             # print('basic_indicators：',basic_indicators)
#             # print('independent_indicators:',independent_indicators)
#             response_data = {
#                 "filledQuestionnaireCount": len(latest_responses),
#                 "adjustedTopBasicIndicators": basic_indicators,
#                 "adjustedTopIndependentIndicators": independent_indicators
#                 # "adjustedTopBasicIndicators": basic_indicators[:3],
#                 # "adjustedTopIndependentIndicators": independent_indicators[:2]
#             }

#             # print("返回的最终分析数据: ", response_data)
#             end_time = time.time()  # 记录结束时间
#             print(f"总执行时间: {end_time - start_time} 秒")
#             return JsonResponse(response_data, safe=False)

#         except Exception as e:
#             print(f"错误: {str(e)}")
#             return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

#     def get_severity_description(self, weight):
#         if weight <= 10:
#             return '非常轻微'
#         elif weight <= 15:
#             return '轻度'
#         elif weight <= 40:
#             return '中等'
#         elif weight <= 50:
#             return '较明显'
#         elif weight <= 65:
#             return '明显'
#         else:
#             return '非常明显'

from asgiref.sync import sync_to_async
class AnalyzeQuestionnaireView(View):
    MAX_WEIGHTS = {
        '气虚': 160.8, '气滞': 152.4, '阳虚': 102, '痰症': 128.4, '实热': 98.4,
        '阴虚': 57.6, '实寒': 68.4, '湿症': 94.8, '血虚': 63.6, '血瘀': 38.4,
        '上热下寒': 20, '热证': 1111, '肺': 62.4, '脾': 295.2, '肝': 214.8,
        '肾': 200.4, '心': 81.6
    }

    # @method_decorator(cache_page(60 * 15))
    async def post(self, request, *args, **kwargs):
        start_time = time.time()
        try:
            user_id = request.user_id

            # 获取最新问卷记录
            latest_timestamps_start = time.time()
            latest_timestamps = await self.get_latest_timestamps(user_id)
            latest_timestamps_end = time.time()
            # print(f"获取最新问卷记录时间: {latest_timestamps_end - latest_timestamps_start} 秒")

            # 获取最新响应
            latest_responses_start = time.time()
            latest_responses = await self.get_latest_responses(user_id, latest_timestamps)
            latest_responses_end = time.time()
            # print(f"获取最新响应时间: {latest_responses_end - latest_responses_start} 秒")

            # 因为 explain() 方法在异步环境中可能不可用，所以我们将其注释掉
            # print(await sync_to_async(latest_timestamps.explain)())

            total_scores, max_weights = await self.process_responses(latest_responses)

            final_results = [{"text": k, "probability": v['totalProbability'], "weight": v['totalWeight'], "type": v['type']} for k, v in total_scores.items()]

            basic_indicators, independent_indicators = await self.process_indicators(final_results)

            response_data = {
                "filledQuestionnaireCount": len(latest_responses),
                "adjustedTopBasicIndicators": basic_indicators,
                "adjustedTopIndependentIndicators": independent_indicators
            }

            end_time = time.time()
            print(f"总执行时间: {end_time - start_time} 秒")
            return JsonResponse(response_data, safe=False)

        except Exception as e:
            print(f"错误: {str(e)}")
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

    @sync_to_async
    def get_latest_timestamps(self, user_id):
        return list(QuestionnaireResponse.objects.filter(
            openid=str(user_id),
            questionnaire_id__gte=10,
            questionnaire_id__lte=18
        ).values('questionnaire_id').annotate(
            latest_time=Max('timestamp')
        ).values('questionnaire_id', 'latest_time'))

    @sync_to_async
    def get_latest_responses(self, user_id, latest_timestamps):
        return list(QuestionnaireResponse.objects.filter(
            openid=str(user_id),
            questionnaire_id__gte=10,
            questionnaire_id__lte=18,
            timestamp=Subquery(
                QuestionnaireResponse.objects.filter(
                    questionnaire_id=OuterRef('questionnaire_id'),
                    openid=str(user_id)
                ).values('timestamp')[:1]
            )
        ))

    @sync_to_async
    def process_responses(self, latest_responses):
        total_scores = defaultdict(lambda: {"totalWeight": 0, "totalProbability": 0, "type": None})
        max_weights = {}
        processing_start = time.time()

        for response in latest_responses:
            scores = response.total_scores
            for score in scores:
                text = score['text']
                weight = score['weight']
                probability = score['probability']
                option_type = score['type']

                if text not in total_scores:
                    total_scores[text] = {"totalWeight": 0, "totalProbability": 0, "type": None}
                total_score = total_scores[text]

                total_score['totalWeight'] += weight
                total_score['totalProbability'] += probability

                if total_score['type'] is None:
                    total_score['type'] = option_type

                if text not in max_weights:
                    max_weights[text] = weight
                else:
                    max_weights[text] = max(max_weights[text], weight)

        processing_end = time.time()
        print(f"处理响应时间: {processing_end - processing_start} 秒")
        return total_scores, max_weights

    @sync_to_async
    def process_indicators(self, final_results):
        basic_indicators = []
        independent_indicators = []
        final_processing_start = time.time()

        for item in final_results:
            max_weight = self.MAX_WEIGHTS.get(item['text'], 1)
            adjusted_weight = round((item['weight'] / max_weight) * 100, 2)
            severity = self.get_severity_description(adjusted_weight)

            indicator_data = {
                "name": item['text'],
                "type": item['type'],
                "adjustedWeight": adjusted_weight,
                "severity": severity
            }

            if item['type'] == "基础":
                basic_indicators.append(indicator_data)
            else:
                independent_indicators.append(indicator_data)

        basic_indicators.sort(key=lambda x: x['adjustedWeight'], reverse=True)
        independent_indicators.sort(key=lambda x: x['adjustedWeight'], reverse=True)
        final_processing_end = time.time()
        print(f"最终处理时间: {final_processing_end - final_processing_start} 秒")

        return basic_indicators, independent_indicators

    def get_severity_description(self, weight):
        if weight <= 10:
            return '非常轻微'
        elif weight <= 15:
            return '轻度'
        elif weight <= 40:
            return '中等'
        elif weight <= 50:
            return '较明显'
        elif weight <= 65:
            return '明显'
        else:
            return '非常明显'


#体质+五行问卷系统
class AnalyzeQuestionnaireViewcomplexnew(View):
    MAX_WEIGHTS = {
        # 11种基础证型的最大权重
        '气虚': 160.8,
        '气滞': 152.4,
        '阳虚': 102,
        '痰症': 128.4,
        '实热': 98.4,
        '阴虚': 57.6,
        '实寒': 68.4,
        '湿症': 94.8,
        '血虚': 63.6,
        '血瘀': 38.4,
        '上热下寒': 20,#3.6
        '热证': 1111,
        # 五个脏腑的最大权重
        '肺': 62.4,
        '脾': 295.2,
        '肝': 214.8,
        '肾': 200.4,
        '心': 81.6
    }
    ZHENGXING_DETAILS = {
            '气虚': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '气虚与现代医学中的多种疾病状态有关，如慢性疲劳综合征、贫血、免疫系统功能低下、心血管疾病、呼吸系统疾病等。在这些疾病中，患者常表现出体力下降、免疫力减弱、恢复能力差等症状，与中医气虚的表现相似。此外，气虚还可能增加患感冒、肺炎、心血管疾病等疾病的风险，且在这些疾病中，气虚的患者可能比常人更难以恢复，症状可能更严重。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content':
                            '气虚的常见临床表现包括但不限于：体力下降、容易疲劳、声音低微、气短懒言、自汗、面色苍白或萎黄、食欲不振、舌质淡、脉象虚弱等。在女性中可能还会表现为月经不调、经量少等症状。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '气虚的养生调养要点包括：保持充足的休息和睡眠，避免过度劳累；保持均衡的饮食，多摄入富含蛋白质和铁质的食物；适量进行温和的运动，如散步、太极、气功等，以增强体质；避免情绪波动过大，保持心态平和；在中医师的指导下，可以适当使用补气的中药，如人参、黄芪、白术等。'
                    }

                ],

                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content':
                            '气虚的食疗方案主要包括摄入易于消化吸收、能补益气血的食物。建议多食用富含蛋白质的食物，如鸡肉、鸭肉、猪瘦肉、鱼类、豆制品等；同时，可以食用一些具有补气作用的食物，如山药、红枣、桂圆、花生等。烹饪方法以炖、蒸、煮为主，避免过度油炸和辛辣刺激。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content':
                            '气虚的运动方案应以温和、不剧烈、不过度消耗体力为原则。推荐的运动包括太极拳、八段锦、五禽戏、散步、慢跑等。这些运动有助于增强体质、调节气息、促进气血运行。运动量不宜过大，以微汗出、不感到疲劳为宜。'
                    },
                    {
                        'title': '3. 推拿按摩方案',
                        'content':
                            '推拿按摩是中医的一种外治法，通过手法刺激人体的特定穴位或部位，以达到疏通经络、调和气血、平衡阴阳的目的。对于气虚的推拿按摩方案，可以采用轻柔的揉法、摩法、推法等，主要在背部督脉和膀胱经、腹部任脉、足三里、脾经、胃经等部位进行操作。每次按摩时间不宜过长，以患者感到放松为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '艾灸是一种温补阳气的方法，适合治疗气虚。常用的穴位有关元、气海、足三里、神阙等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。艾灸后应适当补充水分，避免上火。'
                    }
                ]
            },

            '血虚': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '血虚与现代医学中的贫血、营养不良、慢性疾病导致的贫血、月经不调等疾病有关。在这些情况下，患者可能会出现血液中红细胞计数或血红蛋白浓度降低的症状，与中医血虚的表现相似。此外，血虚还可能增加患心血管疾病、免疫力下降、皮肤问题（如湿疹、脱发）等疾病的风险，且在这些疾病中，血虚的患者可能比常人更难以恢复，症状可能更严重。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content':
                            '血虚的常见临床表现包括面色苍白或萎黄、唇舌色淡、指甲苍白、头晕眼花、心悸失眠、手足发麻、月经量少色淡、舌质淡白、脉细弱等。这些症状反映了身体缺乏足够的血液滋养。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '血虚的养生调养要点包括保持充足的休息和睡眠，避免过度劳累；保持均衡的饮食，多摄入富含铁质、蛋白质、维生素的食物；适量进行温和的运动，如散步、太极、气功等，以促进血液循环；避免情绪波动过大，保持心态平和。'
                    }
                ],

                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content':
                            '血虚的食疗方案主要是摄入富含铁质、蛋白质和维生素的食物。建议多食用红肉、动物肝脏、鸡蛋、鱼类、豆制品、绿叶蔬菜、黑芝麻、红枣等。烹饪方法以炖、蒸、煮为主，以利于消化吸收。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '血虚的运动方案应以轻柔、不剧烈、能促进血液循环为原则。推荐的运动包括太极拳、瑜伽、散步等。运动量不宜过大，以微汗出、不感到疲劳为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '血虚的推拿方案可以通过按摩来促进血液循环和气血生成。常用的手法有揉法、摩法、推法等，主要在背部督脉、腹部任脉、足三里、血海、肝经、脾经等部位进行操作。每次按摩时间不宜过长，以患者感到放松为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '‘艾灸是一种温补阳气、促进气血运行的方法。对于血虚，可以选择关元、气海、足三里、血海等穴位进行艾灸。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }

                ]
            },

            '阴虚': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '阴虚与现代医学中的多种疾病有关，如高血压、糖尿病、甲状腺功能亢进、更年期综合征、干燥综合征等。这些疾病中的一些症状，如热潮红、盗汗、心悸、失眠等，与中医阴虚的表现相似。此外，阴虚还可能增加患心血管疾病、免疫系统疾病、皮肤病（如银屑病）、神经系统疾病（如失眠、焦虑）等疾病的风险，且在这些疾病中，阴虚的患者可能比常人更难以恢复，症状可能更严重。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content':
                            '阴虚的常见临床表现包括五心烦热（即手心、脚心和胸口发热）、盗汗、夜间出汗、失眠、口干咽燥、大便干燥、小便短赤、舌红少苔、脉细数等。这些症状反映了身体阴液不足，无法滋润和冷却身体。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '阴虚的养生调养要点包括保持充足的休息和睡眠，避免过度劳累和精神压力；保持均衡的饮食，多摄入富含水分和滋阴的食物，如梨、苹果、西瓜、芝麻、豆腐等；适量进行温和的运动，如散步、太极、瑜伽等，以促进血液循环和阴液生成；避免烟酒和辛辣刺激性食物，保持心态平和。'
                    }

                ],

                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content':
                            '阴虚的食疗方案主要是摄入滋阴降火的食物。建议多食用梨、苹果、西瓜、芝麻、豆腐、银耳、枸杞、百合等。烹饪方法以炖、蒸、煮为主，避免过度油炸和辛辣刺激。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '阴虚的运动方案应以轻柔、不剧烈、能促进阴液生成为原则。推荐的运动包括太极拳、瑜伽、散步等。运动量不宜过大，以微汗出、不感到疲劳为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '阴虚的推拿方案可以通过按摩来促进血液循环和阴液生成。常用的手法有揉法、摩法、推法等，主要在背部督脉、腹部任脉、足三里、三阴交、肝经、肾经等部位进行操作。每次按摩时间不宜过长，以患者感到放松为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '阴虚的艾灸方案应选择具有滋阴作用的穴位，如肾俞、太溪、三阴交等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },

            '阳虚': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '阳虚与现代医学中的多种疾病有关，如慢性疲劳综合征、甲状腺功能减退、贫血、免疫力低下、心血管疾病等。阳虚体质的人可能更容易感冒，且恢复较慢，同时在心血管疾病方面，如高血压和冠心病，可能会表现出更严重的症状。此外，阳虚还与消化系统疾病如胃溃疡和十二指肠溃疡有关，因为这些疾病的患者常常伴有脾胃虚寒的症状。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content':
                            '阳虚的常见临床表现包括畏寒怕冷、手脚冰凉、面色苍白、精神不振、乏力、腹泻或便秘、小便清长、舌淡胖有齿痕、脉沉迟等。这些症状反映了身体阳气不足，无法提供足够的热量和动力。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '阳虚的养生调养要点包括保持充足的休息和睡眠，避免过度劳累和精神压力；保持均衡的饮食，多摄入温热性质和补阳的食物，如羊肉、牛肉、韭菜、桂圆、核桃等；适量进行温和的运动，如散步、慢跑、太极等，以增强体质和促进血液循环；避免寒凉食物和过度贪凉，保持心态平和。'
                    }
                ],

                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content':
                            '阳虚的食疗方案主要是摄入温热性质和补阳的食物。建议多食用羊肉、牛肉、韭菜、桂圆、核桃、姜、蒜等。烹饪方法以炖、蒸、煮为主，避免过度油炸和寒凉食物。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '阳虚的运动方案应以温和、不剧烈、能促进阳气生成为原则。推荐的运动包括太极拳、慢跑、瑜伽等。运动量不宜过大，以微汗出、不感到疲劳为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '阳虚的推拿方案可以通过按摩来促进血液循环和阳气生成。常用的手法有揉法、摩法、推法等，主要在背部督脉、腹部任脉、足三里、肾俞等部位进行操作。每次按摩时间不宜过长，以患者感到放松为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '阳虚的艾灸方案应选择具有补阳作用的穴位，如关元、气海、足三里、肾俞等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
    
                ]
            },

            '实热': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '实热与现代医学中的急性感染性疾病、某些炎症性疾病、代谢性疾病等有关。例如，实热体质的人可能更容易患有急性肺炎、急性胆囊炎、急性肠胃炎等疾病，且病情可能比常人更严重。实热还可能与某些精神系统疾病有关，如急性发热性精神病、癫痫等。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '实热体质人群的常见临床表现包括发热、口渴、烦躁不安、便秘、尿黄、舌红苔黄、脉数有力等。这些症状反映了体内有热邪蓄积，需要清热泻火。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '实热体质人群的养生调养要点包括保持充足的休息，避免过度劳累；保持清淡饮食，避免辛辣、油腻、煎炸等食物，以免助热生火；适量进行中等强度的运动，如快走、慢跑、游泳等，以帮助身体散热；保持良好的生活习惯，避免情绪激动，保持心态平和。'
                    }
                ],

                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content':
                            '实热体质人群的食疗方案应以清热泻火、生津止渴为主。建议多食用绿豆、冬瓜、黄瓜、西瓜、苦瓜、莲藕等清热食物。烹饪方法以清蒸、水煮为主，避免过度油腻和辛辣。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '实热体质人群的运动方案应以中等强度、能促进汗腺分泌、帮助身体散热为原则。推荐的运动包括快走、慢跑、游泳、瑜伽等。运动后应及时补充水分，以防脱水。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '实热症的推拿方案可以通过按摩来清热泻火。常用的手法有揉法、摩法、推法等，主要在背部膀胱经、腹部任脉、曲池、合谷等部位进行操作。每次按摩时间不宜过长，以患者感到舒适为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '实热的艾灸方案应选择具有清热作用的穴位，如曲池、合谷、内关、足三里等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },

            '实寒': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '实寒与现代医学中的多种疾病有关，如感冒、风湿性关节炎、痛经、腹泻等。实寒体质的人可能更容易患感冒，且病情可能比常人更严重。此外，实寒还可能与心血管疾病有关，如心绞痛和心肌梗死，因为这些疾病的患者常常伴有胸痛和心悸等症状。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '实寒患者的常见临床表现包括身体寒冷、四肢不温、腹痛腹泻、畏寒喜暖、舌淡苔白、脉沉紧等。这些症状反映了体内有寒邪蓄积，需要温阳散寒。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '实寒患者的养生调养要点包括保持充足的休息，避免过度劳累；保持温暖，避免受凉和接触冷水；饮食应以温热为主，多食用羊肉、鸡肉、姜、蒜等温热性质的食物；适量进行温和的运动，如散步、慢跑、太极等，以增强体质和促进血液循环。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '实寒的食疗方案应以温阳散寒为主。建议多食用羊肉、鸡肉、生姜、大蒜、葱等温热性质的食物。烹饪方法以炖、蒸、煮为主，避免过度生冷和寒凉食物。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '实寒的运动方案应以温和、能促进血液循环为原则。推荐的运动包括散步、慢跑、太极等。运动量不宜过大，以身体感到温暖舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '实寒的推拿方案可以通过按摩来温阳散寒。常用的手法有揉法、摩法、推法等，主要在背部督脉、腹部任脉、足三里、关元等部位进行操作。每次按摩时间不宜过长，以患者感到放松为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '实寒的艾灸方案应选择具有温阳作用的穴位，如关元、足三里、肾俞、神阙等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },
            '气滞': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '气滞与现代医学中的消化系统疾病、慢性疼痛、心理压力相关。气滞体质的人可能更容易患胃痛、便秘、胸闷、头痛等疾病，气滞体质人群常常叹气，郁郁不得志，多有易怒烦躁，自觉精神压力很大。且这些症状可能比常人更难缓解。长期的情绪压力和心理负担也可能导致气滞，影响身体的正常生理功能。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '气滞的常见临床表现包括胸闷、胁痛、脘腹胀满、嗳气、大便不畅、月经不调、情绪抑郁、舌苔薄白、脉弦等。这些症状反映了体内气的运行不畅，需要行气解郁。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '气滞的养生调养要点包括保持良好的情绪，避免情绪压抑和过度紧张；饮食应以清淡、易消化为主，避免过度油腻和生冷食物；适量进行有助于行气的运动，如散步、太极、瑜伽等；保持规律的作息，保证充足的睡眠。常怀仁善之心，有助于舒缓气机；多在惠风和畅的公园散步，使肝气舒畅，有助于心灵健康。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '气滞的食疗方案应以行气解郁为主。建议多食用萝卜、柑橘、佛手、玫瑰花等行气食物。烹饪方法以清蒸、水煮为主，避免过度油腻和生冷。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '气滞的运动方案应以有助于行气和缓解压力的运动为主。推荐的运动包括散步、太极、瑜伽、舞蹈等。运动量不宜过大，以身体感到舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '气滞的推拿方案可以通过按摩来行气解郁。常用的手法有揉法、摩法、推法等，主要在胸腹部、背部、肩部等部位进行操作。每次按摩时间不宜过长，以患者感到放松为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '气滞的艾灸方案应选择具有行气作用的穴位，如太冲、合谷、足三里、膻中、肝俞等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },
            '血瘀': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '血瘀与现代医学中的心血管疾病、肿瘤、慢性炎症、皮肤病等有关。血瘀体质的人可能更容易患高血压、冠心病、中风等心血管疾病，且这些疾病的症状可能比常人更严重。此外，血瘀还可能与肿瘤的生长和扩散有关，因为血液运行不畅可能促进肿瘤细胞的聚集和生长。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '血瘀的常见临床表现包括疼痛、肿块、皮肤瘀斑、面色晦暗、唇甲紫暗、舌质紫暗或有瘀点、脉涩等。这些症状反映了体内血液运行不畅，需要活血化瘀。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '血瘀的养生调养要点包括保持适当的运动，促进血液循环；保持均衡的饮食，多摄入富含抗氧化剂的食物，如新鲜蔬菜和水果；避免情绪过于激动，减少精神压力；在中医师的指导下，可以适当使用活血化瘀的中药，如丹参、当归、红花等。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '血瘀的食疗方案应以活血化瘀为主。建议多食用山楂、黑木耳、洋葱、茄子、西红柿等活血食物。烹饪方法以炖、蒸、煮为主，避免过度油炸和生冷。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '血瘀的运动方案应以有氧运动和有助于活血化瘀的运动为主。推荐的运动包括快走、慢跑、游泳、太极等。运动量不宜过大，以身体感到舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '血瘀的推拿方案可以通过按摩来活血化瘀。常用的手法有揉法、摩法、推法等，主要在背部膀胱经、腹部任脉、曲池、合谷等部位进行操作。每次按摩时间不宜过长，以患者感到舒适为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '血瘀的艾灸方案应选择具有活血化瘀作用的穴位，如曲池、合谷、足三里、血海、膈俞等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },
            '痰症': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content': '痰症在现代医学中常与慢性支气管炎、哮喘、慢性阻塞性肺疾病（COPD）、慢性胃炎、消化不良等疾病相关。痰症的病理机制涉及呼吸道和消化道的炎症、黏液分泌增多以及免疫功能失调。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '痰症的常见临床表现包括咳嗽、咳痰、胸闷、气短、喉中痰鸣、食欲不振、恶心、呕吐、腹胀、大便黏滞等。痰的颜色和质地可以反映病情的寒热虚实，如白色清稀痰多为寒痰，黄色黏稠痰多为热痰。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content': '痰症的养生调养要点包括：保持室内空气清新，避免烟雾和有害气体；饮食宜清淡，避免油腻、辛辣、生冷食物；适量运动，增强体质，促进痰液排出；保持心情舒畅，避免情绪波动；定期进行中医调理，如按摩、刮痧等，以促进气血流通。'
                    }
                ],

                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '痰症的食疗方案包括：多食用具有化痰作用的食物，如白萝卜、梨、百合、杏仁等；避免食用油腻、甜腻、辛辣刺激性食物；适量饮用温开水，促进痰液排出；可适当食用薏米、山药等健脾化湿的食物，以改善痰湿体质。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '痰症的运动方案包括：进行适量的有氧运动，如散步、慢跑、太极拳等，以增强心肺功能，促进痰液排出；进行呼吸训练，如深呼吸、腹式呼吸等，以改善呼吸功能；避免剧烈运动，以免加重痰症症状。'
                    },
                    {
                        'title': '3. 针灸方案',
                        'content': '痰症的针灸方案包括：选取具有化痰作用的穴位，如丰隆、足三里、肺俞、脾俞等；根据痰症的寒热虚实，选择不同的针刺手法和补泻方法；针灸治疗可以促进气血流通，改善痰湿症状。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content': '痰症的艾灸方案包括：选取具有化痰作用的穴位，如肺俞、脾俞、足三里等；艾灸可以温通经络，散寒化痰，适用于寒痰和虚寒体质的患者；艾灸时应注意避免烫伤，控制艾灸时间和温度。'
                    }
                ]
            },

            '湿症': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '湿症与现代医学中的多种疾病有关，如风湿性关节炎、湿疹、水肿、肥胖等。湿症体质的人可能更容易患这些疾病，且症状可能比常人更严重。长期的不良生活习惯，如饮食不节、过度饮酒等，也可能导致湿症。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content':
                            '湿症的常见临床表现包括身体沉重、肌肉酸痛、关节肿胀、皮肤湿疹、胸闷腹胀、大便溏稀、小便不利、舌苔厚腻、脉濡缓等。这些症状反映了体内湿邪蓄积，需要健脾利湿。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '湿症的养生调养要点包括保持良好的饮食习惯，避免过度饮酒和摄入生冷食物；适量进行有助于健脾利湿的运动，如散步、慢跑、太极等；保持规律的作息，保证充足的睡眠；在中医师的指导下，可以适当使用健脾利湿的中药，如茯苓、泽泻、白术等。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '湿症的食疗方案应以健脾利湿为主。建议多食用薏米、红豆、扁豆、南瓜、茯苓等健脾利湿食物。烹饪方法以炖、蒸、煮为主，避免过度油炸和生冷。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '湿症的运动方案应以温和、有助于健脾利湿的运动为主。推荐的运动包括散步、慢跑、太极、瑜伽等。运动量不宜过大，以身体感到舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '湿症的推拿方案可以通过按摩来健脾利湿。常用的手法有揉法、摩法、推法等，主要在腹部、背部、腿部等部位进行操作。每次按摩时间不宜过长，以患者感到舒适为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '湿症的艾灸方案应选择具有健脾利湿作用的穴位，如足三里、阴陵泉、水分、脾俞等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },
            '上热下寒': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '上热下寒/寒热错杂与现代医学中的消化系统疾病、心血管疾病、内分泌系统疾病等有关。这种体质的人可能同时出现上部热证（如口腔溃疡、痤疮、咽痛等）和下部寒证（如腹泻、腹痛、下肢冰冷等），且这些症状可能比常人更难以调和。此外，这种体质还可能影响身体的免疫系统和内分泌系统的正常功能，从而增加患病的风险。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content':
                            '上热下寒/寒热错杂的常见临床表现包括口腔溃疡、痤疮、腹泻、腹痛、下肢冰冷、情绪波动、失眠、舌苔薄黄而舌根部苔白、脉象复杂等。这些症状反映了体内既有热邪又有寒邪，需要调和寒热。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '上热下寒/寒热错杂的养生调养要点包括保持均衡的饮食，避免辛辣刺激和过于寒凉的食物；适量进行运动，促进气血运行，但应避免过度劳累；保持良好的作息习惯，保证充足的睡眠；在中医师的指导下，可以适当使用调和寒热的中药，如黄连、肉桂、干姜等。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '上热下寒/寒热错杂的食疗方案应以调和寒热为主。建议多食用性质平和的食物，如南瓜、土豆、红枣、枸杞等。烹饪方法以炖、蒸、煮为主，避免过度油炸和寒凉。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '上热下寒/寒热错杂的运动方案应以温和、有助于调和寒热为原则。推荐的运动包括散步、慢跑、太极、瑜伽等。运动量不宜过大，以身体感到舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '上热下寒/寒热错杂的推拿方案可以通过按摩来调和寒热。常用的手法有揉法、摩法、推法等，主要在背部、腹部、腿部等部位进行操作。每次按摩时间不宜过长，以患者感到舒适为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '上热下寒/寒热错杂的艾灸方案应选择具有调和寒热作用的穴位，如中脘、足三里、神阙、关元、百会等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },
            '心': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '在《黄帝内经》中，心被称为"君主之官"，五行属火，主神明。因此，心的功能失调可能与现代医学中的心血管疾病、神经系统疾病、心理疾病等有关。例如，心火亢盛可能导致高血压、心悸、失眠、焦虑等症状。而心阴虚可能与现代医学中的冠心病、心肌缺血、心律失常等疾病有关。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '主证在心的常见临床表现包括心悸、失眠、多梦、烦躁、口苦、舌尖红、脉象细数等。这些症状反映了心的神志功能和血脉运行不畅。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '主证在心人群的养生起居调养要点包括保持充足的休息和睡眠，避免过度劳累和精神压力；保持均衡的饮食，多摄入有益心脏健康的食物，如全谷物、鱼类、坚果等；适量进行运动，如散步、慢跑、太极等，以增强心脏功能；保持心态平和，避免情绪波动过大。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '主证在心人群的食疗方案应以养心益智为主。建议多食用桂圆、红枣、莲子、百合、鱼类等养心食物。烹饪方法以炖、蒸、煮为主，避免过度油炸和生冷。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '主证在心人群的的运动方案应以温和、有助于养心益智为原则。推荐的运动包括散步、慢跑、太极、瑜伽等。运动量不宜过大，以身体感到舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content': '主证在心人群的的针灸方案应选择具有养心益智作用的穴位，如神门、内关、心俞、曲泽等。根据患者的具体情况进行按摩推拿。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '主证在心人群的的艾灸方案应选择具有养心作用的穴位，如神门、内关、心俞、曲泽等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },
            '肝': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '在中医理论中，肝主疏泄，五行属木，与情志、视力、筋骨、血液等方面密切相关。因此，肝的功能失调可能与现代医学中的多种疾病有关，如肝炎、肝硬化、脂肪肝、情绪障碍（如抑郁、焦虑）、视力问题、关节疼痛、血液疾病等。肝火旺可能与现代医学中的高血压、头痛、面红、易怒等症状有关；而肝阴虚可能与现代医学中的慢性肝炎、肝功能异常、视力减退等疾病有关。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '肝（主证在肝）的常见临床表现包括情绪抑郁或易怒、视力模糊、关节疼痛、胁肋胀痛、口苦、舌边红、脉弦等。这些症状反映了肝的疏泄功能和藏血功能失调。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '肝（主证在肝）的养生起居调养要点包括保持良好的情绪，避免情绪压抑和过度紧张；饮食应以清淡、易消化为主，避免过度油腻和生冷食物；适量进行有助于疏肝解郁的运动，如散步、慢跑、太极等；保持规律的作息，保证充足的睡眠。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content':
                            '肝（主证在肝）的食疗方案应以疏肝解郁、养血柔肝为主。建议多食用绿叶蔬菜、绿豆、黄瓜、番茄、枸杞、红枣等食物。烹饪方法以清蒸、水煮为主，避免过度油炸和生冷。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '肝（主证在肝）的运动方案应以温和、有助于疏肝解郁为原则。推荐的运动包括散步、慢跑、太极、瑜伽等。运动量不宜过大，以身体感到舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '肝（主证在肝）的推拿方案可以通过按摩来疏肝解郁。常用的手法有揉法、摩法、推法等，主要在胸部、胁肋部、腹部等部位进行操作。每次按摩时间不宜过长，以患者感到放松为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '肝（主证在肝）的艾灸方案应选择具有疏肝解郁作用的穴位，如太冲、肝俞、足三里、行间等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },
            '脾': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '在中医理论中，脾主运化，五行属土，与消化、免疫、血液等方面密切相关。因此，脾的功能失调可能与现代医学中的多种疾病有关，如慢性胃炎、胃肠功能紊乱、糖尿病、贫血、免疫缺陷等。脾虚可能与现代医学中的消化不良、腹胀、乏力、水肿等症状有关；而脾湿可能与现代医学中的肥胖、水肿、风湿性关节炎等疾病有关。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '脾（主证在脾）的常见临床表现包括腹胀、腹泻、食欲不振、乏力、水肿、舌苔白腻、脉缓等。这些症状反映了脾的运化功能和升清降浊功能失调。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '脾（主证在脾）的养生起居调养要点包括保持均衡的饮食，避免过度饮食和生冷食物；适量进行有助于健脾利湿的运动，如散步、慢跑、太极等；保持规律的作息，保证充足的睡眠；在中医师的指导下，可以适当使用健脾利湿的中药，如茯苓、泽泻、白术等。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '脾（主证在脾）的食疗方案应以健脾利湿为主。建议多食用薏米、南瓜、土豆、山药、红枣等健脾利湿食物。烹饪方法以炖、蒸、煮为主，避免过度油炸和生冷。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '脾（主证在脾）的运动方案应以温和、有助于健脾利湿为原则。推荐的运动包括散步、慢跑、太极、瑜伽等。运动量不宜过大，以身体感到舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '脾（主证在脾）的推拿方案可以通过按摩来健脾利湿。常用的手法有揉法、摩法、推法等，主要在腹部、背部、腿部等部位进行操作。每次按摩时间不宜过长，以患者感到舒适为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '脾（主证在脾）的艾灸方案应选择具有健脾利湿作用的穴位，如足三里、阴陵泉、脾俞、神阙等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },
            '肺': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '在中医理论中，肺主气，司呼吸，五行属金，与皮肤、呼吸、水液代谢等方面密切相关。因此，肺的功能失调可能与现代医学中的多种疾病有关，如肺炎、哮喘、慢性阻塞性肺疾病（COPD）、皮肤病、水肿等。肺气虚可能与现代医学中的呼吸困难、咳嗽、乏力等症状有关；而肺燥可能与现代医学中的干咳、皮肤干燥等症状有关。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '肺的常见临床表现包括咳嗽、气喘、呼吸急促、皮肤干燥、唇干、舌红苔薄、脉浮等。这些症状反映了肺的呼吸功能和调节水液功能失调。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '肺的养生起居调养要点包括保持良好的空气质量，避免吸烟和有害气体；适量进行有助于增强肺功能的运动，如散步、慢跑、太极、瑜伽等；保持充足的水分摄入，避免干燥的环境；在中医师的指导下，可以适当使用补肺益气的药物，如人参、黄芪、沙参等。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '肺的食疗方案应以补肺益气为主。建议多食用银耳、百合、白萝卜、梨、核桃等补肺食物。烹饪方法以炖、蒸、煮为主，避免过度油炸和生冷。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '肺的运动方案应以温和、有助于增强肺功能为原则。推荐的运动包括散步、慢跑、太极、瑜伽等。运动量不宜过大，以身体感到舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '肺的推拿方案可以通过按摩来补肺益气。常用的手法有揉法、摩法、推法等，主要在胸部、背部、手臂等部位进行操作。每次按摩时间不宜过长，以患者感到舒适为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '肺的艾灸方案应选择具有补肺作用的穴位，如肺俞、大椎、膻中、足三里等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },
            '肾': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '在中医理论中，肾藏精，五行属水，与生长发育、生殖、泌尿、内分泌等方面密切相关。因此，肾的功能失调可能与现代医学中的多种疾病有关，如慢性肾病、高血压、糖尿病、内分泌失调、生殖系统疾病等。肾阳虚可能与现代医学中的腰膝酸软、阳痿、早泄等症状有关；而肾阴虚可能与现代医学中的头晕耳鸣、潮热盗汗、骨蒸潮热等症状有关。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '主证在肾的常见临床表现包括腰膝酸软、头晕耳鸣、记忆力减退、性功能减退、夜尿增多、舌淡苔白、脉沉等。这些症状反映了肾的藏精和生殖功能失调。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '主证在肾的养生起居调养要点包括保持充足的休息和睡眠，避免过度劳累；保持均衡的饮食，多摄入有益肾脏健康的食物，如黑芝麻、核桃、瘦肉、鱼类等；适量进行运动，如散步、慢跑、太极等，以增强肾脏功能；保持心态平和，避免情绪波动过大。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '主证在肾的食疗方案应以补肾为主。建议多食用黑芝麻、核桃、枸杞、红枣、黑豆、羊肉等补肾食物。烹饪方法以炖、蒸、煮为主，避免过度油炸和生冷。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '主证在肾的运动方案应以温和、有助于补肾为原则。推荐的运动包括散步、慢跑、太极、瑜伽等。运动量不宜过大，以身体感到舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '主证在肾的推拿方案可以通过按摩来补肾。常用的手法有揉法、摩法、推法等，主要在腰部、背部、腿部等部位进行操作。每次按摩时间不宜过长，以患者感到舒适为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '主证在肾的艾灸方案应选择具有补肾作用的穴位，如肾俞、太溪、关元、足三里等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            }}
    async def post(self, request, *args, **kwargs):
        start_time = time.time()
        try:
            user_id = request.user_id

            # 获取最新问卷记录
            latest_timestamps_start = time.time()
            latest_timestamps = await self.get_latest_timestamps(user_id)
            latest_timestamps_end = time.time()
            print(f"获取最新问卷记录时间: {latest_timestamps_end - latest_timestamps_start} 秒")

            # 获取最新响应
            latest_responses_start = time.time()
            latest_responses = await self.get_latest_responses(user_id, latest_timestamps)
            latest_responses_end = time.time()
            print(f"获取最新响应时间: {latest_responses_end - latest_responses_start} 秒")

            # 因为 explain() 方法在异步环境中可能不可用，所以我们将其注释掉
            # print(await sync_to_async(latest_timestamps.explain)())

            total_scores, max_weights = await self.process_responses(latest_responses)

            final_results = [{"text": k, "probability": v['totalProbability'], "weight": v['totalWeight'], "type": v['type']} for k, v in total_scores.items()]

            basic_indicators, independent_indicators = await self.process_indicators(final_results)

            response_data = {
                "filledQuestionnaireCount": len(latest_responses),
                "adjustedTopBasicIndicators": basic_indicators,
                "adjustedTopIndependentIndicators": independent_indicators
            }

            end_time = time.time()
            print(f"总执行时间: {end_time - start_time} 秒")
            return JsonResponse(response_data, safe=False)

        except Exception as e:
            print(f"错误: {str(e)}")
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

    @sync_to_async
    def get_latest_timestamps(self, user_id):
        return list(QuestionnaireResponse.objects.filter(
            openid=str(user_id),
            questionnaire_id__gte=10,
            questionnaire_id__lte=18
        ).values('questionnaire_id').annotate(
            latest_time=Max('timestamp')
        ).values('questionnaire_id', 'latest_time'))

    @sync_to_async
    def get_latest_responses(self, user_id, latest_timestamps):
        return list(QuestionnaireResponse.objects.filter(
            openid=str(user_id),
            questionnaire_id__gte=10,
            questionnaire_id__lte=18,
            timestamp=Subquery(
                QuestionnaireResponse.objects.filter(
                    questionnaire_id=OuterRef('questionnaire_id'),
                    openid=str(user_id)
                ).values('timestamp')[:1]
            )
        ))

    @sync_to_async
    def process_responses(self, latest_responses):
        total_scores = defaultdict(lambda: {"totalWeight": 0, "totalProbability": 0, "type": None})
        max_weights = {}
        processing_start = time.time()

        for response in latest_responses:
            scores = response.total_scores
            for score in scores:
                text = score['text']
                weight = score['weight']
                probability = score['probability']
                option_type = score['type']

                if text not in total_scores:
                    total_scores[text] = {"totalWeight": 0, "totalProbability": 0, "type": None}
                total_score = total_scores[text]

                total_score['totalWeight'] += weight
                total_score['totalProbability'] += probability

                if total_score['type'] is None:
                    total_score['type'] = option_type

                if text not in max_weights:
                    max_weights[text] = weight
                else:
                    max_weights[text] = max(max_weights[text], weight)

        processing_end = time.time()
        print(f"处理响应时间: {processing_end - processing_start} 秒")
        return total_scores, max_weights

    @sync_to_async
    def process_indicators(self, final_results):
        basic_indicators = []
        independent_indicators = []
        final_processing_start = time.time()

        for item in final_results:
            max_weight = self.MAX_WEIGHTS.get(item['text'], 1)
            adjusted_weight = round((item['weight'] / max_weight) * 100, 2)
            severity = self.get_severity_description(adjusted_weight)

            indicator_data = {
                "name": item['text'],
                "type": item['type'],
                "adjustedWeight": adjusted_weight,
                "severity": severity
            }

            if item['type'] == "基础":
                basic_indicators.append(indicator_data)
            else:
                independent_indicators.append(indicator_data)

        basic_indicators.sort(key=lambda x: x['adjustedWeight'], reverse=True)
        independent_indicators.sort(key=lambda x: x['adjustedWeight'], reverse=True)
        final_processing_end = time.time()
        print(f"最终处理时间: {final_processing_end - final_processing_start} 秒")

        return basic_indicators, independent_indicators

    def get_severity_description(self, weight):
        if weight <= 10:
            return '非常轻微'
        elif weight <= 15:
            return '轻度'
        elif weight <= 40:
            return '中等'
        elif weight <= 50:
            return '较明显'
        elif weight <= 65:
            return '明显'
        else:
            return '非常明显'


class AnalyzeQuestionnaireViewcomplex(View):
    MAX_WEIGHTS = {
        # 11种基础证型的最大权重
        '气虚': 160.8,
        '气滞': 152.4,
        '阳虚': 102,
        '痰症': 128.4,
        '实热': 98.4,
        '阴虚': 57.6,
        '实寒': 68.4,
        '湿症': 94.8,
        '血虚': 63.6,
        '血瘀': 38.4,
        '上热下寒': 20,#3.6
        '热证': 1111,
        # 五个脏腑的最大权重
        '肺': 62.4,
        '脾': 295.2,
        '肝': 214.8,
        '肾': 200.4,
        '心': 81.6
    }
    ZHENGXING_DETAILS = {
            '气虚': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '气虚与现代医学中的多种疾病状态有关，如慢性疲劳综合征、贫血、免疫系统功能低下、心血管疾病、呼吸系统疾病等。在这些疾病中，患者常表现出体力下降、免疫力减弱、恢复能力差等症状，与中医气虚的表现相似。此外，气虚还可能增加患感冒、肺炎、心血管疾病等疾病的风险，且在这些疾病中，气虚的患者可能比常人更难以恢复，症状可能更严重。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content':
                            '气虚的常见临床表现包括但不限于：体力下降、容易疲劳、声音低微、气短懒言、自汗、面色苍白或萎黄、食欲不振、舌质淡、脉象虚弱等。在女性中可能还会表现为月经不调、经量少等症状。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '气虚的养生调养要点包括：保持充足的休息和睡眠，避免过度劳累；保持均衡的饮食，多摄入富含蛋白质和铁质的食物；适量进行温和的运动，如散步、太极、气功等，以增强体质；避免情绪波动过大，保持心态平和；在中医师的指导下，可以适当使用补气的中药，如人参、黄芪、白术等。'
                    }

                ],

                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content':
                            '气虚的食疗方案主要包括摄入易于消化吸收、能补益气血的食物。建议多食用富含蛋白质的食物，如鸡肉、鸭肉、猪瘦肉、鱼类、豆制品等；同时，可以食用一些具有补气作用的食物，如山药、红枣、桂圆、花生等。烹饪方法以炖、蒸、煮为主，避免过度油炸和辛辣刺激。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content':
                            '气虚的运动方案应以温和、不剧烈、不过度消耗体力为原则。推荐的运动包括太极拳、八段锦、五禽戏、散步、慢跑等。这些运动有助于增强体质、调节气息、促进气血运行。运动量不宜过大，以微汗出、不感到疲劳为宜。'
                    },
                    {
                        'title': '3. 推拿按摩方案',
                        'content':
                            '推拿按摩是中医的一种外治法，通过手法刺激人体的特定穴位或部位，以达到疏通经络、调和气血、平衡阴阳的目的。对于气虚的推拿按摩方案，可以采用轻柔的揉法、摩法、推法等，主要在背部督脉和膀胱经、腹部任脉、足三里、脾经、胃经等部位进行操作。每次按摩时间不宜过长，以患者感到放松为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '艾灸是一种温补阳气的方法，适合治疗气虚。常用的穴位有关元、气海、足三里、神阙等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。艾灸后应适当补充水分，避免上火。'
                    }
                ]
            },

            '血虚': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '血虚与现代医学中的贫血、营养不良、慢性疾病导致的贫血、月经不调等疾病有关。在这些情况下，患者可能会出现血液中红细胞计数或血红蛋白浓度降低的症状，与中医血虚的表现相似。此外，血虚还可能增加患心血管疾病、免疫力下降、皮肤问题（如湿疹、脱发）等疾病的风险，且在这些疾病中，血虚的患者可能比常人更难以恢复，症状可能更严重。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content':
                            '血虚的常见临床表现包括面色苍白或萎黄、唇舌色淡、指甲苍白、头晕眼花、心悸失眠、手足发麻、月经量少色淡、舌质淡白、脉细弱等。这些症状反映了身体缺乏足够的血液滋养。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '血虚的养生调养要点包括保持充足的休息和睡眠，避免过度劳累；保持均衡的饮食，多摄入富含铁质、蛋白质、维生素的食物；适量进行温和的运动，如散步、太极、气功等，以促进血液循环；避免情绪波动过大，保持心态平和。'
                    }
                ],

                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content':
                            '血虚的食疗方案主要是摄入富含铁质、蛋白质和维生素的食物。建议多食用红肉、动物肝脏、鸡蛋、鱼类、豆制品、绿叶蔬菜、黑芝麻、红枣等。烹饪方法以炖、蒸、煮为主，以利于消化吸收。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '血虚的运动方案应以轻柔、不剧烈、能促进血液循环为原则。推荐的运动包括太极拳、瑜伽、散步等。运动量不宜过大，以微汗出、不感到疲劳为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '血虚的推拿方案可以通过按摩来促进血液循环和气血生成。常用的手法有揉法、摩法、推法等，主要在背部督脉、腹部任脉、足三里、血海、肝经、脾经等部位进行操作。每次按摩时间不宜过长，以患者感到放松为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '‘艾灸是一种温补阳气、促进气血运行的方法。对于血虚，可以选择关元、气海、足三里、血海等穴位进行艾灸。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }

                ]
            },

            '阴虚': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '阴虚与现代医学中的多种疾病有关，如高血压、糖尿病、甲状腺功能亢进、更年期综合征、干燥综合征等。这些疾病中的一些症状，如热潮红、盗汗、心悸、失眠等，与中医阴虚的表现相似。此外，阴虚还可能增加患心血管疾病、免疫系统疾病、皮肤病（如银屑病）、神经系统疾病（如失眠、焦虑）等疾病的风险，且在这些疾病中，阴虚的患者可能比常人更难以恢复，症状可能更严重。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content':
                            '阴虚的常见临床表现包括五心烦热（即手心、脚心和胸口发热）、盗汗、夜间出汗、失眠、口干咽燥、大便干燥、小便短赤、舌红少苔、脉细数等。这些症状反映了身体阴液不足，无法滋润和冷却身体。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '阴虚的养生调养要点包括保持充足的休息和睡眠，避免过度劳累和精神压力；保持均衡的饮食，多摄入富含水分和滋阴的食物，如梨、苹果、西瓜、芝麻、豆腐等；适量进行温和的运动，如散步、太极、瑜伽等，以促进血液循环和阴液生成；避免烟酒和辛辣刺激性食物，保持心态平和。'
                    }

                ],

                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content':
                            '阴虚的食疗方案主要是摄入滋阴降火的食物。建议多食用梨、苹果、西瓜、芝麻、豆腐、银耳、枸杞、百合等。烹饪方法以炖、蒸、煮为主，避免过度油炸和辛辣刺激。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '阴虚的运动方案应以轻柔、不剧烈、能促进阴液生成为原则。推荐的运动包括太极拳、瑜伽、散步等。运动量不宜过大，以微汗出、不感到疲劳为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '阴虚的推拿方案可以通过按摩来促进血液循环和阴液生成。常用的手法有揉法、摩法、推法等，主要在背部督脉、腹部任脉、足三里、三阴交、肝经、肾经等部位进行操作。每次按摩时间不宜过长，以患者感到放松为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '阴虚的艾灸方案应选择具有滋阴作用的穴位，如肾俞、太溪、三阴交等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },

            '阳虚': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '阳虚与现代医学中的多种疾病有关，如慢性疲劳综合征、甲状腺功能减退、贫血、免疫力低下、心血管疾病等。阳虚体质的人可能更容易感冒，且恢复较慢，同时在心血管疾病方面，如高血压和冠心病，可能会表现出更严重的症状。此外，阳虚还与消化系统疾病如胃溃疡和十二指肠溃疡有关，因为这些疾病的患者常常伴有脾胃虚寒的症状。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content':
                            '阳虚的常见临床表现包括畏寒怕冷、手脚冰凉、面色苍白、精神不振、乏力、腹泻或便秘、小便清长、舌淡胖有齿痕、脉沉迟等。这些症状反映了身体阳气不足，无法提供足够的热量和动力。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '阳虚的养生调养要点包括保持充足的休息和睡眠，避免过度劳累和精神压力；保持均衡的饮食，多摄入温热性质和补阳的食物，如羊肉、牛肉、韭菜、桂圆、核桃等；适量进行温和的运动，如散步、慢跑、太极等，以增强体质和促进血液循环；避免寒凉食物和过度贪凉，保持心态平和。'
                    }
                ],

                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content':
                            '阳虚的食疗方案主要是摄入温热性质和补阳的食物。建议多食用羊肉、牛肉、韭菜、桂圆、核桃、姜、蒜等。烹饪方法以炖、蒸、煮为主，避免过度油炸和寒凉食物。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '阳虚的运动方案应以温和、不剧烈、能促进阳气生成为原则。推荐的运动包括太极拳、慢跑、瑜伽等。运动量不宜过大，以微汗出、不感到疲劳为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '阳虚的推拿方案可以通过按摩来促进血液循环和阳气生成。常用的手法有揉法、摩法、推法等，主要在背部督脉、腹部任脉、足三里、肾俞等部位进行操作。每次按摩时间不宜过长，以患者感到放松为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '阳虚的艾灸方案应选择具有补阳作用的穴位，如关元、气海、足三里、肾俞等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
    
                ]
            },

            '实热': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '实热与现代医学中的急性感染性疾病、某些炎症性疾病、代谢性疾病等有关。例如，实热体质的人可能更容易患有急性肺炎、急性胆囊炎、急性肠胃炎等疾病，且病情可能比常人更严重。实热还可能与某些精神系统疾病有关，如急性发热性精神病、癫痫等。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '实热体质人群的常见临床表现包括发热、口渴、烦躁不安、便秘、尿黄、舌红苔黄、脉数有力等。这些症状反映了体内有热邪蓄积，需要清热泻火。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '实热体质人群的养生调养要点包括保持充足的休息，避免过度劳累；保持清淡饮食，避免辛辣、油腻、煎炸等食物，以免助热生火；适量进行中等强度的运动，如快走、慢跑、游泳等，以帮助身体散热；保持良好的生活习惯，避免情绪激动，保持心态平和。'
                    }
                ],

                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content':
                            '实热体质人群的食疗方案应以清热泻火、生津止渴为主。建议多食用绿豆、冬瓜、黄瓜、西瓜、苦瓜、莲藕等清热食物。烹饪方法以清蒸、水煮为主，避免过度油腻和辛辣。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '实热体质人群的运动方案应以中等强度、能促进汗腺分泌、帮助身体散热为原则。推荐的运动包括快走、慢跑、游泳、瑜伽等。运动后应及时补充水分，以防脱水。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '实热症的推拿方案可以通过按摩来清热泻火。常用的手法有揉法、摩法、推法等，主要在背部膀胱经、腹部任脉、曲池、合谷等部位进行操作。每次按摩时间不宜过长，以患者感到舒适为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '实热的艾灸方案应选择具有清热作用的穴位，如曲池、合谷、内关、足三里等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },

            '实寒': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '实寒与现代医学中的多种疾病有关，如感冒、风湿性关节炎、痛经、腹泻等。实寒体质的人可能更容易患感冒，且病情可能比常人更严重。此外，实寒还可能与心血管疾病有关，如心绞痛和心肌梗死，因为这些疾病的患者常常伴有胸痛和心悸等症状。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '实寒患者的常见临床表现包括身体寒冷、四肢不温、腹痛腹泻、畏寒喜暖、舌淡苔白、脉沉紧等。这些症状反映了体内有寒邪蓄积，需要温阳散寒。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '实寒患者的养生调养要点包括保持充足的休息，避免过度劳累；保持温暖，避免受凉和接触冷水；饮食应以温热为主，多食用羊肉、鸡肉、姜、蒜等温热性质的食物；适量进行温和的运动，如散步、慢跑、太极等，以增强体质和促进血液循环。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '实寒的食疗方案应以温阳散寒为主。建议多食用羊肉、鸡肉、生姜、大蒜、葱等温热性质的食物。烹饪方法以炖、蒸、煮为主，避免过度生冷和寒凉食物。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '实寒的运动方案应以温和、能促进血液循环为原则。推荐的运动包括散步、慢跑、太极等。运动量不宜过大，以身体感到温暖舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '实寒的推拿方案可以通过按摩来温阳散寒。常用的手法有揉法、摩法、推法等，主要在背部督脉、腹部任脉、足三里、关元等部位进行操作。每次按摩时间不宜过长，以患者感到放松为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '实寒的艾灸方案应选择具有温阳作用的穴位，如关元、足三里、肾俞、神阙等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },
            '气滞': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '气滞与现代医学中的消化系统疾病、慢性疼痛、心理压力相关。气滞体质的人可能更容易患胃痛、便秘、胸闷、头痛等疾病，气滞体质人群常常叹气，郁郁不得志，多有易怒烦躁，自觉精神压力很大。且这些症状可能比常人更难缓解。长期的情绪压力和心理负担也可能导致气滞，影响身体的正常生理功能。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '气滞的常见临床表现包括胸闷、胁痛、脘腹胀满、嗳气、大便不畅、月经不调、情绪抑郁、舌苔薄白、脉弦等。这些症状反映了体内气的运行不畅，需要行气解郁。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '气滞的养生调养要点包括保持良好的情绪，避免情绪压抑和过度紧张；饮食应以清淡、易消化为主，避免过度油腻和生冷食物；适量进行有助于行气的运动，如散步、太极、瑜伽等；保持规律的作息，保证充足的睡眠。常怀仁善之心，有助于舒缓气机；多在惠风和畅的公园散步，使肝气舒畅，有助于心灵健康。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '气滞的食疗方案应以行气解郁为主。建议多食用萝卜、柑橘、佛手、玫瑰花等行气食物。烹饪方法以清蒸、水煮为主，避免过度油腻和生冷。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '气滞的运动方案应以有助于行气和缓解压力的运动为主。推荐的运动包括散步、太极、瑜伽、舞蹈等。运动量不宜过大，以身体感到舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '气滞的推拿方案可以通过按摩来行气解郁。常用的手法有揉法、摩法、推法等，主要在胸腹部、背部、肩部等部位进行操作。每次按摩时间不宜过长，以患者感到放松为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '气滞的艾灸方案应选择具有行气作用的穴位，如太冲、合谷、足三里、膻中、肝俞等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },
            '血瘀': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '血瘀与现代医学中的心血管疾病、肿瘤、慢性炎症、皮肤病等有关。血瘀体质的人可能更容易患高血压、冠心病、中风等心血管疾病，且这些疾病的症状可能比常人更严重。此外，血瘀还可能与肿瘤的生长和扩散有关，因为血液运行不畅可能促进肿瘤细胞的聚集和生长。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '血瘀的常见临床表现包括疼痛、肿块、皮肤瘀斑、面色晦暗、唇甲紫暗、舌质紫暗或有瘀点、脉涩等。这些症状反映了体内血液运行不畅，需要活血化瘀。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '血瘀的养生调养要点包括保持适当的运动，促进血液循环；保持均衡的饮食，多摄入富含抗氧化剂的食物，如新鲜蔬菜和水果；避免情绪过于激动，减少精神压力；在中医师的指导下，可以适当使用活血化瘀的中药，如丹参、当归、红花等。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '血瘀的食疗方案应以活血化瘀为主。建议多食用山楂、黑木耳、洋葱、茄子、西红柿等活血食物。烹饪方法以炖、蒸、煮为主，避免过度油炸和生冷。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '血瘀的运动方案应以有氧运动和有助于活血化瘀的运动为主。推荐的运动包括快走、慢跑、游泳、太极等。运动量不宜过大，以身体感到舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '血瘀的推拿方案可以通过按摩来活血化瘀。常用的手法有揉法、摩法、推法等，主要在背部膀胱经、腹部任脉、曲池、合谷等部位进行操作。每次按摩时间不宜过长，以患者感到舒适为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '血瘀的艾灸方案应选择具有活血化瘀作用的穴位，如曲池、合谷、足三里、血海、膈俞等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },
            '痰症': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content': '痰症在现代医学中常与慢性支气管炎、哮喘、慢性阻塞性肺疾病（COPD）、慢性胃炎、消化不良等疾病相关。痰症的病理机制涉及呼吸道和消化道的炎症、黏液分泌增多以及免疫功能失调。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '痰症的常见临床表现包括咳嗽、咳痰、胸闷、气短、喉中痰鸣、食欲不振、恶心、呕吐、腹胀、大便黏滞等。痰的颜色和质地可以反映病情的寒热虚实，如白色清稀痰多为寒痰，黄色黏稠痰多为热痰。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content': '痰症的养生调养要点包括：保持室内空气清新，避免烟雾和有害气体；饮食宜清淡，避免油腻、辛辣、生冷食物；适量运动，增强体质，促进痰液排出；保持心情舒畅，避免情绪波动；定期进行中医调理，如按摩、刮痧等，以促进气血流通。'
                    }
                ],

                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '痰症的食疗方案包括：多食用具有化痰作用的食物，如白萝卜、梨、百合、杏仁等；避免食用油腻、甜腻、辛辣刺激性食物；适量饮用温开水，促进痰液排出；可适当食用薏米、山药等健脾化湿的食物，以改善痰湿体质。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '痰症的运动方案包括：进行适量的有氧运动，如散步、慢跑、太极拳等，以增强心肺功能，促进痰液排出；进行呼吸训练，如深呼吸、腹式呼吸等，以改善呼吸功能；避免剧烈运动，以免加重痰症症状。'
                    },
                    {
                        'title': '3. 针灸方案',
                        'content': '痰症的针灸方案包括：选取具有化痰作用的穴位，如丰隆、足三里、肺俞、脾俞等；根据痰症的寒热虚实，选择不同的针刺手法和补泻方法；针灸治疗可以促进气血流通，改善痰湿症状。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content': '痰症的艾灸方案包括：选取具有化痰作用的穴位，如肺俞、脾俞、足三里等；艾灸可以温通经络，散寒化痰，适用于寒痰和虚寒体质的患者；艾灸时应注意避免烫伤，控制艾灸时间和温度。'
                    }
                ]
            },

            '湿症': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '湿症与现代医学中的多种疾病有关，如风湿性关节炎、湿疹、水肿、肥胖等。湿症体质的人可能更容易患这些疾病，且症状可能比常人更严重。长期的不良生活习惯，如饮食不节、过度饮酒等，也可能导致湿症。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content':
                            '湿症的常见临床表现包括身体沉重、肌肉酸痛、关节肿胀、皮肤湿疹、胸闷腹胀、大便溏稀、小便不利、舌苔厚腻、脉濡缓等。这些症状反映了体内湿邪蓄积，需要健脾利湿。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '湿症的养生调养要点包括保持良好的饮食习惯，避免过度饮酒和摄入生冷食物；适量进行有助于健脾利湿的运动，如散步、慢跑、太极等；保持规律的作息，保证充足的睡眠；在中医师的指导下，可以适当使用健脾利湿的中药，如茯苓、泽泻、白术等。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '湿症的食疗方案应以健脾利湿为主。建议多食用薏米、红豆、扁豆、南瓜、茯苓等健脾利湿食物。烹饪方法以炖、蒸、煮为主，避免过度油炸和生冷。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '湿症的运动方案应以温和、有助于健脾利湿的运动为主。推荐的运动包括散步、慢跑、太极、瑜伽等。运动量不宜过大，以身体感到舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '湿症的推拿方案可以通过按摩来健脾利湿。常用的手法有揉法、摩法、推法等，主要在腹部、背部、腿部等部位进行操作。每次按摩时间不宜过长，以患者感到舒适为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '湿症的艾灸方案应选择具有健脾利湿作用的穴位，如足三里、阴陵泉、水分、脾俞等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },
            '上热下寒': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '上热下寒/寒热错杂与现代医学中的消化系统疾病、心血管疾病、内分泌系统疾病等有关。这种体质的人可能同时出现上部热证（如口腔溃疡、痤疮、咽痛等）和下部寒证（如腹泻、腹痛、下肢冰冷等），且这些症状可能比常人更难以调和。此外，这种体质还可能影响身体的免疫系统和内分泌系统的正常功能，从而增加患病的风险。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content':
                            '上热下寒/寒热错杂的常见临床表现包括口腔溃疡、痤疮、腹泻、腹痛、下肢冰冷、情绪波动、失眠、舌苔薄黄而舌根部苔白、脉象复杂等。这些症状反映了体内既有热邪又有寒邪，需要调和寒热。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '上热下寒/寒热错杂的养生调养要点包括保持均衡的饮食，避免辛辣刺激和过于寒凉的食物；适量进行运动，促进气血运行，但应避免过度劳累；保持良好的作息习惯，保证充足的睡眠；在中医师的指导下，可以适当使用调和寒热的中药，如黄连、肉桂、干姜等。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '上热下寒/寒热错杂的食疗方案应以调和寒热为主。建议多食用性质平和的食物，如南瓜、土豆、红枣、枸杞等。烹饪方法以炖、蒸、煮为主，避免过度油炸和寒凉。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '上热下寒/寒热错杂的运动方案应以温和、有助于调和寒热为原则。推荐的运动包括散步、慢跑、太极、瑜伽等。运动量不宜过大，以身体感到舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '上热下寒/寒热错杂的推拿方案可以通过按摩来调和寒热。常用的手法有揉法、摩法、推法等，主要在背部、腹部、腿部等部位进行操作。每次按摩时间不宜过长，以患者感到舒适为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '上热下寒/寒热错杂的艾灸方案应选择具有调和寒热作用的穴位，如中脘、足三里、神阙、关元、百会等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },
            '心': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '在《黄帝内经》中，心被称为"君主之官"，五行属火，主神明。因此，心的功能失调可能与现代医学中的心血管疾病、神经系统疾病、心理疾病等有关。例如，心火亢盛可能导致高血压、心悸、失眠、焦虑等症状。而心阴虚可能与现代医学中的冠心病、心肌缺血、心律失常等疾病有关。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '主证在心的常见临床表现包括心悸、失眠、多梦、烦躁、口苦、舌尖红、脉象细数等。这些症状反映了心的神志功能和血脉运行不畅。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '主证在心人群的养生起居调养要点包括保持充足的休息和睡眠，避免过度劳累和精神压力；保持均衡的饮食，多摄入有益心脏健康的食物，如全谷物、鱼类、坚果等；适量进行运动，如散步、慢跑、太极等，以增强心脏功能；保持心态平和，避免情绪波动过大。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '主证在心人群的食疗方案应以养心益智为主。建议多食用桂圆、红枣、莲子、百合、鱼类等养心食物。烹饪方法以炖、蒸、煮为主，避免过度油炸和生冷。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '主证在心人群的的运动方案应以温和、有助于养心益智为原则。推荐的运动包括散步、慢跑、太极、瑜伽等。运动量不宜过大，以身体感到舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content': '主证在心人群的的针灸方案应选择具有养心益智作用的穴位，如神门、内关、心俞、曲泽等。根据患者的具体情况进行按摩推拿。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '主证在心人群的的艾灸方案应选择具有养心作用的穴位，如神门、内关、心俞、曲泽等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },
            '肝': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '在中医理论中，肝主疏泄，五行属木，与情志、视力、筋骨、血液等方面密切相关。因此，肝的功能失调可能与现代医学中的多种疾病有关，如肝炎、肝硬化、脂肪肝、情绪障碍（如抑郁、焦虑）、视力问题、关节疼痛、血液疾病等。肝火旺可能与现代医学中的高血压、头痛、面红、易怒等症状有关；而肝阴虚可能与现代医学中的慢性肝炎、肝功能异常、视力减退等疾病有关。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '肝（主证在肝）的常见临床表现包括情绪抑郁或易怒、视力模糊、关节疼痛、胁肋胀痛、口苦、舌边红、脉弦等。这些症状反映了肝的疏泄功能和藏血功能失调。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '肝（主证在肝）的养生起居调养要点包括保持良好的情绪，避免情绪压抑和过度紧张；饮食应以清淡、易消化为主，避免过度油腻和生冷食物；适量进行有助于疏肝解郁的运动，如散步、慢跑、太极等；保持规律的作息，保证充足的睡眠。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content':
                            '肝（主证在肝）的食疗方案应以疏肝解郁、养血柔肝为主。建议多食用绿叶蔬菜、绿豆、黄瓜、番茄、枸杞、红枣等食物。烹饪方法以清蒸、水煮为主，避免过度油炸和生冷。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '肝（主证在肝）的运动方案应以温和、有助于疏肝解郁为原则。推荐的运动包括散步、慢跑、太极、瑜伽等。运动量不宜过大，以身体感到舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '肝（主证在肝）的推拿方案可以通过按摩来疏肝解郁。常用的手法有揉法、摩法、推法等，主要在胸部、胁肋部、腹部等部位进行操作。每次按摩时间不宜过长，以患者感到放松为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '肝（主证在肝）的艾灸方案应选择具有疏肝解郁作用的穴位，如太冲、肝俞、足三里、行间等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },
            '脾': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '在中医理论中，脾主运化，五行属土，与消化、免疫、血液等方面密切相关。因此，脾的功能失调可能与现代医学中的多种疾病有关，如慢性胃炎、胃肠功能紊乱、糖尿病、贫血、免疫缺陷等。脾虚可能与现代医学中的消化不良、腹胀、乏力、水肿等症状有关；而脾湿可能与现代医学中的肥胖、水肿、风湿性关节炎等疾病有关。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '脾（主证在脾）的常见临床表现包括腹胀、腹泻、食欲不振、乏力、水肿、舌苔白腻、脉缓等。这些症状反映了脾的运化功能和升清降浊功能失调。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '脾（主证在脾）的养生起居调养要点包括保持均衡的饮食，避免过度饮食和生冷食物；适量进行有助于健脾利湿的运动，如散步、慢跑、太极等；保持规律的作息，保证充足的睡眠；在中医师的指导下，可以适当使用健脾利湿的中药，如茯苓、泽泻、白术等。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '脾（主证在脾）的食疗方案应以健脾利湿为主。建议多食用薏米、南瓜、土豆、山药、红枣等健脾利湿食物。烹饪方法以炖、蒸、煮为主，避免过度油炸和生冷。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '脾（主证在脾）的运动方案应以温和、有助于健脾利湿为原则。推荐的运动包括散步、慢跑、太极、瑜伽等。运动量不宜过大，以身体感到舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '脾（主证在脾）的推拿方案可以通过按摩来健脾利湿。常用的手法有揉法、摩法、推法等，主要在腹部、背部、腿部等部位进行操作。每次按摩时间不宜过长，以患者感到舒适为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '脾（主证在脾）的艾灸方案应选择具有健脾利湿作用的穴位，如足三里、阴陵泉、脾俞、神阙等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },
            '肺': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '在中医理论中，肺主气，司呼吸，五行属金，与皮肤、呼吸、水液代谢等方面密切相关。因此，肺的功能失调可能与现代医学中的多种疾病有关，如肺炎、哮喘、慢性阻塞性肺疾病（COPD）、皮肤病、水肿等。肺气虚可能与现代医学中的呼吸困难、咳嗽、乏力等症状有关；而肺燥可能与现代医学中的干咳、皮肤干燥等症状有关。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '肺的常见临床表现包括咳嗽、气喘、呼吸急促、皮肤干燥、唇干、舌红苔薄、脉浮等。这些症状反映了肺的呼吸功能和调节水液功能失调。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '肺的养生起居调养要点包括保持良好的空气质量，避免吸烟和有害气体；适量进行有助于增强肺功能的运动，如散步、慢跑、太极、瑜伽等；保持充足的水分摄入，避免干燥的环境；在中医师的指导下，可以适当使用补肺益气的药物，如人参、黄芪、沙参等。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '肺的食疗方案应以补肺益气为主。建议多食用银耳、百合、白萝卜、梨、核桃等补肺食物。烹饪方法以炖、蒸、煮为主，避免过度油炸和生冷。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '肺的运动方案应以温和、有助于增强肺功能为原则。推荐的运动包括散步、慢跑、太极、瑜伽等。运动量不宜过大，以身体感到舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '肺的推拿方案可以通过按摩来补肺益气。常用的手法有揉法、摩法、推法等，主要在胸部、背部、手臂等部位进行操作。每次按摩时间不宜过长，以患者感到舒适为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '肺的艾灸方案应选择具有补肺作用的穴位，如肺俞、大椎、膻中、足三里等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            },
            '肾': {
                'clinicalManifestation': [
                    {
                        'title': '1. 与现代医学疾病的关联',
                        'content':
                            '在中医理论中，肾藏精，五行属水，与生长发育、生殖、泌尿、内分泌等方面密切相关。因此，肾的功能失调可能与现代医学中的多种疾病有关，如慢性肾病、高血压、糖尿病、内分泌失调、生殖系统疾病等。肾阳虚可能与现代医学中的腰膝酸软、阳痿、早泄等症状有关；而肾阴虚可能与现代医学中的头晕耳鸣、潮热盗汗、骨蒸潮热等症状有关。'
                    },
                    {
                        'title': '2. 常见的临床表现',
                        'content': '主证在肾的常见临床表现包括腰膝酸软、头晕耳鸣、记忆力减退、性功能减退、夜尿增多、舌淡苔白、脉沉等。这些症状反映了肾的藏精和生殖功能失调。'
                    },
                    {
                        'title': '3. 养生起居调养要点',
                        'content':
                            '主证在肾的养生起居调养要点包括保持充足的休息和睡眠，避免过度劳累；保持均衡的饮食，多摄入有益肾脏健康的食物，如黑芝麻、核桃、瘦肉、鱼类等；适量进行运动，如散步、慢跑、太极等，以增强肾脏功能；保持心态平和，避免情绪波动过大。'
                    }
                ],
                'intervention': [
                    {
                        'title': '1. 食疗方案',
                        'content': '主证在肾的食疗方案应以补肾为主。建议多食用黑芝麻、核桃、枸杞、红枣、黑豆、羊肉等补肾食物。烹饪方法以炖、蒸、煮为主，避免过度油炸和生冷。'
                    },
                    {
                        'title': '2. 运动方案',
                        'content': '主证在肾的运动方案应以温和、有助于补肾为原则。推荐的运动包括散步、慢跑、太极、瑜伽等。运动量不宜过大，以身体感到舒适为宜。'
                    },
                    {
                        'title': '3. 推拿方案',
                        'content':
                            '主证在肾的推拿方案可以通过按摩来补肾。常用的手法有揉法、摩法、推法等，主要在腰部、背部、腿部等部位进行操作。每次按摩时间不宜过长，以患者感到舒适为宜。'
                    },
                    {
                        'title': '4. 艾灸方案',
                        'content':
                            '主证在肾的艾灸方案应选择具有补肾作用的穴位，如肾俞、太溪、关元、足三里等。艾灸时应注意控制艾条与皮肤的距离，以局部感到温热而不灼痛为宜。每次艾灸的时间不宜过长，一般每个穴位灸5-15分钟。'
                    }
                ]
            }}
    def post(self, request, *args, **kwargs):
        try:
            user_id = request.user_id
            print(f"接收到的用户 ID: {user_id}")

            # latest_timestamps = QuestionnaireResponse.objects.filter(
            #     openid=str(user_id)
            # ).values('questionnaire_id').annotate(
            #     latest_time=Max('timestamp')
            # ).values('questionnaire_id', 'latest_time')

            # latest_responses = QuestionnaireResponse.objects.filter(
            #     openid=str(user_id),
            #     timestamp=Subquery(
            #         latest_timestamps.filter(questionnaire_id=OuterRef('questionnaire_id')).values('latest_time')
            #     )
            # )
            latest_timestamps = QuestionnaireResponse.objects.filter(
            openid=str(user_id),
            questionnaire_id__gte=10,  # 筛选 questionnaire_id 大于等于 10
            questionnaire_id__lte=18   # 筛选 questionnaire_id 小于等于 18
        ).values('questionnaire_id').annotate(
            latest_time=Max('timestamp')
        ).values('questionnaire_id', 'latest_time')

            latest_responses = QuestionnaireResponse.objects.filter(
                openid=str(user_id),
                questionnaire_id__gte=10,  # 筛选 questionnaire_id 大于等于 10
                questionnaire_id__lte=18,  # 筛选 questionnaire_id 小于等于 18
                timestamp=Subquery(
                    latest_timestamps.filter(questionnaire_id=OuterRef('questionnaire_id')).values('latest_time')
                )
            )
            print(f"获取到的最新问卷记录: {latest_responses}")

            total_scores = defaultdict(lambda: {"totalWeight": 0, "totalProbability": 0, "type": None})

            max_weights = {}  # 记录每个项目的最大权重

            for response in latest_responses:
                scores = response.total_scores
                print(f"处理问卷 ID: {response.questionnaire_id}, 答案: {response.answers}")

                for score in scores:
                    text = score['text']
                    option_type = score['type']
                    total_scores[text]['totalWeight'] += score['weight']
                    total_scores[text]['totalProbability'] += score['probability']

                    if total_scores[text]['type'] is None:
                        total_scores[text]['type'] = option_type

                    max_weights[text] = max(max_weights.get(text, 0), score['weight'])

            final_results = [{"text": k, "probability": v['totalProbability'], "weight": v['totalWeight'], "type": v['type']} for k, v in total_scores.items()]
            print('完整结果：', final_results)

            basic_indicators = []
            independent_indicators = []

            for item in final_results:
                max_weight = self.MAX_WEIGHTS.get(item['text'], 1)
                adjusted_weight = round((item['weight'] / max_weight) * 100, 2)
                severity = self.get_severity_description(adjusted_weight)
                zhengxing_detail = self.ZHENGXING_DETAILS.get(item['text'], {})
                # print('循环中测试证型返回',zhengxing_detail)
                indicator_data = {
                    "name": item['text'],
                    "type":item['type'],
                    # "probability": item['probability'],
                    # "weight": item['weight'],
                    "adjustedWeight": adjusted_weight,
                    "severity": severity,
                    "intervention": zhengxing_detail.get('intervention', []),  # 仅返回相关症型的临床表现和干预方案
                    "clinicalManifestation": zhengxing_detail.get('clinicalManifestation', [])  # 仅返回相关症型的临床表现和干预方案

                }

                if item['type'] == "基础":
                    basic_indicators.append(indicator_data)
                else:
                    independent_indicators.append(indicator_data)

            basic_indicators.sort(key=lambda x: x['adjustedWeight'], reverse=True)
            independent_indicators.sort(key=lambda x: x['adjustedWeight'], reverse=True)

            response_data = {
                "filledQuestionnaireCount": len(latest_responses),
                "adjustedTopBasicIndicators": basic_indicators[:3],
                "adjustedTopIndependentIndicators": independent_indicators[:2]
            }

            print("返回的最终分析数据: ", response_data)
            return JsonResponse(response_data, safe=False)

        except Exception as e:
            print(f"错误: {str(e)}")
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

    def get_severity_description(self, weight):
        if weight <= 10:
            return '非常轻微'
        elif weight <= 15:
            return '轻度'
        elif weight <= 40:
            return '中等'
        elif weight <= 50:
            return '较明显'
        elif weight <= 65:
            return '明显'
        else:
            return '非常明显'










class ActiveAnnouncementView(APIView):
    def get(self, request):
        try:
            now = timezone.now()
            active_announcements = Announcement.objects.filter(
                is_active=True,
                start_date__lte=now,
                end_date__gte=now
            ).order_by('-start_date')  # 按开始日期降序排序，最新的公告排在前面
            
            if active_announcements.exists():
                serializer = AnnouncementSerializer(active_announcements, many=True)
                return Response(serializer.data)
            return Response({"message": "No active announcements"}, status=status.HTTP_404_NOT_FOUND)
            
        except Exception as e:
            error_message = f"Error in ActiveAnnouncementsView: {str(e)}"
            print(error_message)  # 打印到控制台
            logger.error(error_message)  # 记录到日志文件
            return Response({"error": "An unexpected error occurred"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# #空请求异步ninja            
# from ninja import Router
# from datetime import datetime

# # 创建一个路由器
# bank_router = Router()

# @bank_router.post("/EmptyRequestView")
# async def empty_request(request):
#     """处理空请求以刷新token"""
#     print(f'[{datetime.now()}] 空请求以刷新token')
#     return {"message": "Empty request OK"}

class EmptyRequestView(APIView):
    def post(self, request):
        start_time = time.time()
        print('空请求以刷新token')
        end_time = time.time()
        print(f"请求耗时: {end_time - start_time:.6f} 秒")
        return Response({"message": "Empty request OK"})



@csrf_exempt
@require_http_methods(["POST"])
def update_symptoms(request):
    try:
        print("开始处理更新症状请求")
        data = json.loads(request.body)
        print(f"接收到的数据: {data}")
        
        symptoms_data = data.get('symptoms', [])
        print(f"症状数据: {symptoms_data}")
        
        questionnaire_id = data.get('questionnaireId')
        print(f"问卷ID: {questionnaire_id}")
        
        user_id = request.user_id
        print(f"用户ID: {user_id}")

        user = UserInfo.objects.get(id=user_id)
        print(f"找到用户: {user}")

        # 删除该用户该问卷的所有现有症状
        deleted_count = UserSymptom.objects.filter(user=user, questionnaire_id=questionnaire_id).delete()
        print(f"删除的症状数量: {deleted_count}")

        # 插入新的症状
        new_symptoms = [
            UserSymptom(
                user=user,
                symptom=symptom_data['text'],
                level=symptom_data['level'],
                questionnaire_id=questionnaire_id
            )
            for symptom_data in symptoms_data
        ]
        print(f"准备创建的新症状: {new_symptoms}")
        
        created_symptoms = UserSymptom.objects.bulk_create(new_symptoms)
        print(f"成功创建的症状数量: {len(created_symptoms)}")

        # 验证症状是否成功保存
        saved_symptoms = UserSymptom.objects.filter(user=user, questionnaire_id=questionnaire_id)
        print(f"数据库中保存的症状: {list(saved_symptoms.values())}")

        return JsonResponse({'status': 'success', 'message': 'Symptoms updated successfully'})
    except UserInfo.DoesNotExist:
        print(f"未找到用户，用户ID: {user_id}")
        return JsonResponse({'status': 'error', 'message': 'User not found'}, status=404)
    except Exception as e:
        print(f"发生异常: {str(e)}")
        import traceback
        print(f"异常堆栈: {traceback.format_exc()}")
        return JsonResponse({'status': 'error', 'message': str(e)}, status=400)

@require_http_methods(["GET"])
def get_all_symptoms(request):
    try:
        user_id = request.user_id
        user = UserInfo.objects.get(id=user_id)
        symptoms = UserSymptom.objects.filter(user=user).values('symptom', 'level', 'questionnaire_id')
        return JsonResponse({'status': 'success', 'symptoms': list(symptoms)})
    except UserInfo.DoesNotExist:
        error_message = f"User with id {user_id} not found"
        
        return JsonResponse({'status': 'error', 'message': 'User not found'}, status=404)
    except Exception as e:
        error_message = f"An error occurred: {str(e)}\n\nTraceback:\n{traceback.format_exc()}"
        
        print(error_message)  # 在控制台打印错误信息
        return JsonResponse({'status': 'error', 'message': 'An unexpected error occurred'}, status=500)

@require_http_methods(["DELETE"])
def delete_all_symptoms(request):
    try:
        user_id = request.user_id
        user = UserInfo.objects.get(id=user_id)
        deleted_count, _ = UserSymptom.objects.filter(user=user).delete()
        return JsonResponse({
            'status': 'success',
            'message': f'Successfully deleted {deleted_count} symptoms.',
        })
    except UserInfo.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'User not found'
        }, status=404)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)


